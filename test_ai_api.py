#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析API的脚本
"""

import requests
import json

def test_ai_analysis_api():
    """测试AI分析数据API"""
    base_url = "http://localhost:5000"
    
    print("🧪 测试AI分析数据API")
    print("=" * 50)
    
    # 测试参数
    test_params = {
        'time_range': '0',  # 今天
        'station': 'SOC',   # SOC测试站
        'slave': '',        # 所有设备
        'limit': 10         # Top 10
    }
    
    try:
        # 1. 测试AI分析数据API
        print("📊 测试 /api/lp8155/ai_analysis_data")
        url = f"{base_url}/api/lp8155/ai_analysis_data"
        response = requests.get(url, params=test_params, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            if data.get('success'):
                analysis_data = data['data']
                print(f"📈 数据统计:")
                print(f"   - 总测试数: {analysis_data['statistics']['total_tests']}")
                print(f"   - 失败数: {analysis_data['statistics']['failed_tests']}")
                print(f"   - Top Issues数量: {len(analysis_data['top_issues'])}")
                
                if analysis_data['top_issues']:
                    print(f"🔍 Top 3 失败项:")
                    for i, issue in enumerate(analysis_data['top_issues'][:3]):
                        percentage = float(issue['percentage']) if isinstance(issue['percentage'], str) else issue['percentage']
                        print(f"   {i+1}. {issue['failure_item']}: {issue['count']}次 ({percentage:.2f}%)")
                
                print(f"📅 分析时间范围: {analysis_data['summary']['analysis_period']}")
                
                # 测试数据是否包含失败数据
                if analysis_data['statistics']['failed_tests'] > 0:
                    print("✅ 包含失败数据，适合AI分析")
                else:
                    print("⚠️  没有失败数据，AI分析可能无意义")
                    
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    print("\n" + "=" * 50)
    
    # 2. 测试AI配置API
    print("⚙️  测试 /api/ai_config")
    try:
        url = f"{base_url}/api/ai_config"
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            config_data = response.json()
            print("✅ AI配置获取成功")
            print(f"   - 提供商: {config_data.get('provider', 'unknown')}")
            print(f"   - 模型: {config_data.get('config', {}).get('model', 'unknown')}")
            print(f"   - 客户端调用: {config_data.get('client_side_enabled', False)}")
        else:
            print(f"❌ AI配置获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ AI配置测试错误: {e}")
    
    print("\n" + "=" * 50)
    
    # 3. 测试余额查询API
    print("💰 测试 /api/deepseek/balance")
    try:
        url = f"{base_url}/api/deepseek/balance"
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            balance_data = response.json()
            print("✅ 余额查询成功")
            
            if balance_data.get('success'):
                balance = balance_data['balance']
                if balance.get('balance_infos') and len(balance['balance_infos']) > 0:
                    balance_info = balance['balance_infos'][0]
                    total_balance = balance_info.get('total_balance', '0.00')
                    print(f"   - 余额: {total_balance}元")
                else:
                    print("   - 余额信息格式异常")
            else:
                print(f"   - 余额查询失败: {balance_data.get('error', '未知错误')}")
        else:
            print(f"❌ 余额查询失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 余额查询测试错误: {e}")

def test_direct_deepseek_api():
    """直接测试DeepSeek API"""
    print("\n" + "=" * 50)
    print("🌐 直接测试DeepSeek API")
    
    try:
        url = "https://api.deepseek.com/v1/chat/completions"
        headers = {
            'Authorization': 'Bearer sk-b28e0b5d4412410db203c87809ccb9ad',
            'Content-Type': 'application/json'
        }
        
        data = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的汽车测试数据分析专家。"
                },
                {
                    "role": "user",
                    "content": "请简单介绍一下汽车EOL测试的重要性。"
                }
            ],
            "max_tokens": 200,
            "temperature": 0.3
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('choices') and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print("✅ DeepSeek API调用成功")
                print(f"响应内容: {content[:100]}...")
            else:
                print("❌ DeepSeek API响应格式异常")
        else:
            print(f"❌ DeepSeek API调用失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ DeepSeek API测试错误: {e}")

if __name__ == "__main__":
    test_ai_analysis_api()
    test_direct_deepseek_api()
    print("\n🎯 测试完成！")
