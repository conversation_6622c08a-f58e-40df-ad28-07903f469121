#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析修复效果
"""

import requests
import json
import time

def test_direct_deepseek():
    """测试直接调用DeepSeek API"""
    print("🧪 测试直接调用DeepSeek API")
    print("=" * 50)
    
    try:
        response = requests.post("https://api.deepseek.com/v1/chat/completions",
                               headers={
                                   "Authorization": "Bearer sk-b28e0b5d4412410db203c87809ccb9ad",
                                   "Content-Type": "application/json"
                               },
                               json={
                                   "model": "deepseek-chat",
                                   "messages": [
                                       {
                                           "role": "user",
                                           "content": "请简单介绍汽车EOL测试，不超过50字。"
                                       }
                                   ],
                                   "max_tokens": 100,
                                   "temperature": 0.3
                               })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print(f"✅ AI响应: {content}")
            return True
        else:
            print(f"❌ 错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_server_proxy():
    """测试服务器代理"""
    print("\n🔄 测试服务器代理")
    print("=" * 50)
    
    try:
        response = requests.post("http://localhost:5000/api/deepseek/proxy",
                               headers={
                                   "Content-Type": "application/json"
                               },
                               json={
                                   "model": "deepseek-chat",
                                   "messages": [
                                       {
                                           "role": "user",
                                           "content": "请简单介绍汽车EOL测试，不超过50字。"
                                       }
                                   ],
                                   "max_tokens": 100,
                                   "temperature": 0.3
                               })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print(f"✅ 代理响应: {content}")
            return True
        else:
            print(f"❌ 错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_balance_query():
    """测试余额查询"""
    print("\n💰 测试余额查询")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:5000/api/deepseek/balance")
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            print(f"✅ 余额查询成功: {data}")
            return True
        else:
            print(f"❌ 错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def main():
    print("🚀 开始测试AI分析修复效果")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    # 测试余额查询
    balance_ok = test_balance_query()
    
    # 测试直接调用
    direct_ok = test_direct_deepseek()
    
    # 测试服务器代理
    proxy_ok = test_server_proxy()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  余额查询: {'✅ 成功' if balance_ok else '❌ 失败'}")
    print(f"  直接调用: {'✅ 成功' if direct_ok else '❌ 失败'}")
    print(f"  服务器代理: {'✅ 成功' if proxy_ok else '❌ 失败'}")
    
    if proxy_ok:
        print("\n🎉 服务器代理工作正常，可以解决CORS问题！")
    elif direct_ok:
        print("\n🎉 直接调用工作正常！")
    else:
        print("\n⚠️ 需要进一步调试网络连接问题")
    
    print("\n💡 建议:")
    if not direct_ok and proxy_ok:
        print("  - 浏览器可能有CORS限制，使用服务器代理是最佳方案")
    elif direct_ok:
        print("  - 直接调用正常，客户端AI调用应该可以工作")
    else:
        print("  - 检查网络连接和API密钥")

if __name__ == "__main__":
    main()
