#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试详细失败数据收集
"""

import requests
import json
import traceback

def test_detailed_failures():
    """测试详细失败数据收集"""
    try:
        print("🔍 测试详细失败数据收集...")
        
        # 测试AI分析数据API
        url = "http://localhost:5000/api/lp8155/ai_analysis_data"
        params = {
            'time_range': '1',
            'station': '',
            'slave': '',
            'limit': '10'
        }
        
        print(f"📡 请求URL: {url}")
        print(f"📋 请求参数: {params}")
        
        response = requests.get(url, params=params)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            if data['success']:
                analysis_data = data['data']
                print(f"📈 数据结构: {list(analysis_data.keys())}")
                
                # 检查是否有详细失败数据
                if 'detailed_failures' in analysis_data:
                    detailed_failures = analysis_data['detailed_failures']
                    print(f"🔍 详细失败数据数量: {len(detailed_failures)}")
                    
                    for i, failure in enumerate(detailed_failures[:2]):
                        print(f"\n📋 失败项 {i+1}: {failure['failure_item']}")
                        print(f"   - 失败次数: {failure['count']}")
                        percentage = float(failure['percentage']) if failure['percentage'] is not None else 0.0
                        print(f"   - 失败比例: {percentage:.2f}%")
                        
                        if 'details' in failure and failure['details']:
                            print(f"   - 详细案例数量: {len(failure['details'])}")
                            
                            for j, detail in enumerate(failure['details'][:2]):
                                print(f"     案例 {j+1}:")
                                print(f"       - 主项: {detail.get('main_item', 'N/A')}")
                                print(f"       - 子项: {detail.get('sub_item', 'N/A')}")
                                print(f"       - 标准值: {detail.get('standard_value', 'N/A')}")
                                print(f"       - 测试值: {detail.get('measured_value', 'N/A')}")
                                print(f"       - 下限: {detail.get('low_limit', 'N/A')}")
                                print(f"       - 上限: {detail.get('high_limit', 'N/A')}")
                                print(f"       - 失败原因: {detail.get('failure_reason', 'N/A')}")
                else:
                    print("❌ 没有找到详细失败数据")
                    
            else:
                print(f"❌ API返回失败: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_detailed_failures()
