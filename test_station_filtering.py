#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试站位过滤功能
"""

import requests
import json
import time

def test_station_data(station_name, station_value):
    """测试特定站位的数据"""
    print(f"\n🔍 测试 {station_name} 站位数据...")
    
    try:
        response = requests.get("http://localhost:5000/api/lp8155/ai_analysis_data", 
                              params={
                                  'time_range': '7',
                                  'station': station_value,
                                  'slave': '',
                                  'limit': '5'
                              })
        
        print(f"  状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            if data.get('success'):
                analysis_data = data['data']
                print(f"  ✅ 数据获取成功:")
                print(f"    - 总测试数: {analysis_data.get('total_tests', 0)}")
                print(f"    - 失败测试数: {analysis_data.get('failed_tests', 0)}")
                print(f"    - 失败率: {analysis_data.get('fail_rate', 0)}%")
                print(f"    - Top问题数量: {len(analysis_data.get('top_issues', []))}")
                
                # 显示前3个问题
                for i, issue in enumerate(analysis_data.get('top_issues', [])[:3]):
                    print(f"    - Top{i+1}: {issue['failure_item']} ({issue['count']}次)")
                
                return analysis_data
            else:
                print(f"  ❌ 数据获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
            print(f"  响应内容: {response.text}")
            
    except Exception as e:
        print(f"  ❌ 请求异常: {str(e)}")
    
    return None

def main():
    print("🚀 开始测试站位过滤功能")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试不同站位
    stations = [
        ("全部站位", ""),
        ("SOC测试", "SOC"),
        ("VIU测试", "VIU")
    ]
    
    results = {}
    for station_name, station_value in stations:
        results[station_name] = test_station_data(station_name, station_value)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 站位过滤测试结果总结:")
    
    for station_name, data in results.items():
        if data:
            total = data.get('total_tests', 0)
            failed = data.get('failed_tests', 0)
            print(f"  {station_name}: ✅ 成功 (总数:{total}, 失败:{failed})")
        else:
            print(f"  {station_name}: ❌ 失败")
    
    # 检查数据合理性
    print("\n🔍 数据合理性检查:")
    all_data = results.get("全部站位")
    soc_data = results.get("SOC测试")
    viu_data = results.get("VIU测试")
    
    if all_data and soc_data and viu_data:
        all_total = all_data.get('total_tests', 0)
        soc_total = soc_data.get('total_tests', 0)
        viu_total = viu_data.get('total_tests', 0)
        
        print(f"  全部站位总数: {all_total}")
        print(f"  SOC + VIU总数: {soc_total + viu_total}")
        
        if soc_total > 0 and viu_total > 0:
            print("  ✅ SOC和VIU都有数据，过滤功能正常")
        elif soc_total > 0:
            print("  ⚠️ 只有SOC有数据，VIU可能没有数据")
        elif viu_total > 0:
            print("  ⚠️ 只有VIU有数据，SOC可能没有数据")
        else:
            print("  ❌ SOC和VIU都没有数据，可能有问题")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    main()
