#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建版本管理脚本
用于在打包exe前手动递增版本号
"""

import json
import os
from datetime import datetime

VERSION_FILE = 'version.json'

def load_version_info():
    """加载版本信息"""
    try:
        if os.path.exists(VERSION_FILE):
            with open(VERSION_FILE, 'r', encoding='utf-8') as f:
                version_info = json.load(f)
                # 清理旧的build_time字段，确保使用新的字段结构
                if 'build_time' in version_info:
                    del version_info['build_time']
                # 确保必要字段存在
                if 'service_start_time' not in version_info:
                    version_info['service_start_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return version_info
        else:
            # 默认版本信息
            return {
                'version': 'v1.0.0',
                'release_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'service_start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'build_count': 0
            }
    except Exception as e:
        print(f"加载版本信息失败: {str(e)}")
        return {
            'version': 'v1.0.0',
            'release_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'service_start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'build_count': 0
        }

def save_version_info(version_info):
    """保存版本信息"""
    try:
        with open(VERSION_FILE, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        print(f"版本信息已保存到 {VERSION_FILE}")
    except Exception as e:
        print(f"保存版本信息失败: {str(e)}")

def increment_build_version():
    """递增构建版本号"""
    try:
        version_info = load_version_info()
        build_count = version_info.get('build_count', 0)
        
        # 递增构建次数
        build_count += 1
        
        # 生成新版本号 (主版本.次版本.构建次数)
        major = 1
        minor = 0
        new_version = f"v{major}.{minor}.{build_count}"
        
        # 更新版本信息 - 发布日期固定为打包时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        version_info.update({
            'version': new_version,
            'release_date': current_time,  # 发布日期设为打包时间
            'service_start_time': current_time,  # 初始服务启动时间
            'build_count': build_count
        })
        
        save_version_info(version_info)
        print(f"版本已更新为: {new_version}")
        print(f"发布日期: {current_time}")
        print(f"构建次数: {build_count}")
        return version_info
    except Exception as e:
        print(f"版本递增失败: {str(e)}")
        return None

def show_current_version():
    """显示当前版本信息"""
    version_info = load_version_info()
    print("当前版本信息:")
    print(f"  版本号: {version_info.get('version', 'v1.0.0')}")
    print(f"  发布日期: {version_info.get('release_date', 'N/A')}")
    print(f"  服务启动: {version_info.get('service_start_time', 'N/A')}")
    print(f"  构建次数: {version_info.get('build_count', 0)}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'increment':
        print("正在递增版本号...")
        increment_build_version()
    else:
        show_current_version()
        print("\n使用方法:")
        print("  python build_version.py          # 显示当前版本信息")
        print("  python build_version.py increment # 递增版本号")
