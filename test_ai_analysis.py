#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析功能测试脚本
"""

import requests
import json
import sys

def test_ai_config_api():
    """测试AI配置API"""
    print("=== 测试AI配置API ===")
    
    try:
        # 测试获取配置
        response = requests.get('http://localhost:5000/api/ai_config')
        print(f"GET /api/ai_config - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"配置状态: {'已配置' if data['config']['is_configured'] else '未配置'}")
            print(f"当前提供商: {data['config']['provider']}")
            print(f"可用提供商: {list(data['providers'].keys())}")
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"测试AI配置API失败: {e}")

def test_ai_analysis_api():
    """测试AI分析API"""
    print("\n=== 测试AI分析API ===")
    
    try:
        # 测试AI分析（使用模拟数据）
        params = {
            'time_range': '7',
            'station': '',
            'slave': '',
            'limit': '5'
        }
        
        response = requests.get('http://localhost:5000/api/lp8155/ai_analysis', params=params)
        print(f"GET /api/lp8155/ai_analysis - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("AI分析成功!")
                print(f"分析摘要: {data['analysis']['summary'][:100]}...")
                print(f"关键发现数量: {len(data['analysis']['key_findings'])}")
            else:
                print(f"AI分析失败: {data['error']}")
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"测试AI分析API失败: {e}")

def test_ai_config_update():
    """测试AI配置更新"""
    print("\n=== 测试AI配置更新 ===")
    
    try:
        # 测试配置更新
        config_data = {
            'provider': 'openai',
            'model': 'gpt-3.5-turbo',
            'max_tokens': 2000,
            'temperature': 0.7,
            'cache_enabled': True,
            'cache_duration_hours': 2
        }
        
        response = requests.post(
            'http://localhost:5000/api/ai_config',
            headers={'Content-Type': 'application/json'},
            json=config_data
        )
        
        print(f"POST /api/ai_config - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("配置更新成功!")
                print(f"配置状态: {'已配置' if data['is_configured'] else '未配置'}")
            else:
                print(f"配置更新失败: {data['error']}")
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"测试AI配置更新失败: {e}")

def main():
    """主测试函数"""
    print("开始测试AI分析功能...")
    print("=" * 50)
    
    # 测试AI配置API
    test_ai_config_api()
    
    # 测试AI配置更新
    test_ai_config_update()
    
    # 测试AI分析API
    test_ai_analysis_api()
    
    print("\n" + "=" * 50)
    print("测试完成!")

if __name__ == '__main__':
    main()
