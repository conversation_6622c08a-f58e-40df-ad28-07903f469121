[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:00:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:05:30] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:08:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:09:15] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-10 14:09:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:10:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-10 14:11:37] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:56] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:04:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:05:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:05:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:05:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:09:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:56] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:14:56] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:16:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:16:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/5[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:17:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:19:56] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:21:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:22:15] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:24:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:29:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:34:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:39:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:44:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:49:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:04] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:50:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:52:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:52:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:52:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:55:05] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:55:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:59:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:59:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-18 11:59:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:33] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:24:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:22] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:22] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:25:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-20 16:26:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/5[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:27:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:28:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:28:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:28:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:30:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-20 16:32:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-20 16:32:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:49] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:50] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:50] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:32:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:33:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:33:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:57] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:34:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:35:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:35:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:35:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:35:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:35:56] IP: 127.0.0.1 | Method: GET | Path: /test_top_issues.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:27] IP: 127.0.0.1 | Method: GET | Path: /test_top_issues.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:29] IP: 127.0.0.1 | Method: GET | Path: /test_top_issues.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:36:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:37:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:20] IP: 127.0.0.1 | Method: GET | Path: /debug_top_issues.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:38:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:45] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:46] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:46] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:46] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:39:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:40:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:40:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:40:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:42:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:46] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:44:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:45:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:45:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:46:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:48:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:49:35] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:49:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:49:46] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:49:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:51:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:51:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:53:20] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 16:53:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 17:06:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 17:06:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 17:06:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-20 17:06:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:32:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:32:53] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:33:03] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:33:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:34:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:34:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:34:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:34:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:36:31] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:36:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:54] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:37:56] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:38:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:40:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:40:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:40:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:41:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:42:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:42:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:42:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:44:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:44:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:47:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:47:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
6
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:49] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:50] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:50] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:51:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:52:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:52:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:52:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:52:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:52:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:52:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:38] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:39] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:39] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:53:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:54:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:54:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:54:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:55:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:57:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:58:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:58:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 13:59:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:01:33] IP: 127.0.0.1 | Method: GET | Path: /test_failure_detail.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:19] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:20] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:20] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:03:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:04:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:04:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:05:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:06:52] IP: 127.0.0.1 | Method: GET | Path: /simple_test.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:20] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:08:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:09:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:09:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:33] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:34] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:10:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:11:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:12:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:20] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:13:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:14:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:24] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:15:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
ri/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:36:18] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:49] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:37:55] IP: 127.0.0.1 | Method: GET | Path: /api/info | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:38:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:38:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:38:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:38:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:41:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-21 14:42:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-06-21 14:42:50] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:42:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 14:43:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:40] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:05:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:06:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:16] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:46] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:12:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:12:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:12:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:12:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:44] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:14:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:40] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:16:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:40] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:45] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:21:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:26:41] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:26:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:26:45] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:26:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:28:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:30:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:30:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:30:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:31:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:32:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/utils.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/chart-manager.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/leap-project.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/config.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:27] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:34:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safar[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:39:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:43:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:44:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:54] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:54] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-06-21 15:48:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:54] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:48:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:19] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 15:49:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:20:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:21:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:25:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:25:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:29] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:29] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:29:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:30:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:30:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:05] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:32:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-21 19:35:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:53:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:53] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:53] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:54:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:57:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:57:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:58:57] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 10:58:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:03:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:03:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:03:57] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:03:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:05:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:07:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/search_by_sn | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:07:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/search_by_sn | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:09:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:09:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:09:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:10:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:10:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:10:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:44] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:12:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:01] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:01] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:01] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:01] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:01] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:01] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:02] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-06-23 11:13:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:13:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:15:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:15:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:15:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:15:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:18:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:18:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:20:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:20:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:22:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:23:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:23:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:42] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:24:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:25:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:25:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:27:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:27:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:28:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:28:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:29:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:29:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:09] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:18] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:31:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:32:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:32:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:33:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:33:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:36] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:36] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:36] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:36] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

i/537.36
[2025-06-23 11:34:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
/537.36
[2025-06-23 11:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:44] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:34:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:22] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:22] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:35:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:36:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:36:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:36:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:37:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:37:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:38:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-06-23 11:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:44] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:39:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:40:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:40:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:40:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:40:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:41:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:41:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:42:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:42:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:43:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:43:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:11] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36[2[2025-06-23 11:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:44] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:44:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:45:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:45:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:45:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:45:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:13] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:13] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-23 11:46:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-23 11:46:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:46:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:47:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:47:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:48:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:48:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:49:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:49:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:49:44] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:49:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:50:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-23 11:50:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:11] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:12] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-06-24 10:51:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:13] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-06-24 10:51:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:13] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-24 10:51:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:54] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:54] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:54] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:55] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:56] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:54:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:55:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:56:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:56:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:56:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:56:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:56:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:56:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:57:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:57:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:57:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:56] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 14:59:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:00:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:00:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:00:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:41] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:01:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:11] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:12:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:13:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:13:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-30 15:13:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:29] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:30] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:30] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
/537.36
[2025-07-02 10:04:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:30] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:35] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:04:35] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:05:22] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:37] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:38] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:16:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:17] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:18] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:17:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-02 10:24:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:20:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:25] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:33] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:56] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:23:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:24:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:26:35] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:27:47] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:28:04] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:30:26] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:31:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:31:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:32:32] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:36:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:36:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:37:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:37:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:37:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:41:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:41:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:45:19] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:46:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:46:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:40] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:40] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:50:57] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:08] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:51:46] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:52:54] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:56:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 13:56:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:01:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:01:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:06:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:06:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:11:39] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:11:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:13:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:14:03] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:14:33] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:01] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:15:16] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:18:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:18:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:08] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:19:26] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:24:09] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:24:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:29:09] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:29:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:09] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:16] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:34:22] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
6
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:18] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:35:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:40:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:40:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:45:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:45:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:50:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:50:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:55:10] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 14:55:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:23:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:24:42] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:28:42] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:28:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:11] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061
[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:19] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:28] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:42] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:33:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:34:18] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:34:47] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:38:21] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:38:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:40:15] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:40:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:40:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-03 15:40:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:56] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:56] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:58] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 09:59:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:00:20] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:02:02] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:02:33] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:02:47] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:02:57] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:03:06] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:04:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:04:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:09:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:09:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:18] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:27] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:31] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:32] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:34] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:12:37] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:13:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:13:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:13:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:13:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:13:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:13:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:05] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

i/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:30:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:34:53] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:34:55] IP: 127.0.0.1 | Method: POST | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:34:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:35:36] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:35:38] IP: 127.0.0.1 | Method: POST | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:35:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:36:33] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:36:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:36:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:36:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:36:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:36:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:37:17] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:37:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:37:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:37:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:37:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:37:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:39:33] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:40:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:40:37] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:41:04] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:42:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:42:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:42:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:42:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:42:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:42:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:44:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:44:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:46:39] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:49:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:49:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:50:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:51:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:52:46] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:52:48] IP: 127.0.0.1 | Method: POST | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 10:52:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: python-requests/2.32.4
[2025-07-04 10:52:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 10:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:39] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-04 10:54:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:54:49] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:55:05] IP: 127.0.0.1 | Method: POST | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:55:09] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:55:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:55:54] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:17] IP: 127.0.0.1 | Method: POST | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:53] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:53] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:55] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:56:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:58:14] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:58:30] IP: 127.0.0.1 | Method: POST | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:58:34] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:58:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:59:16] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:59:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:59:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 10:59:49] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:00:54] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:01:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:01:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:03:18] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:04:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:04:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:06:40] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:06:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:06:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:09:00] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:09:31] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:09:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:09:43] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:10:16] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:11:49] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:13:03] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:13:37] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:14:31] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:14:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:06] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:14] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:16:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:17:17] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:17:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: python-requests/2.32.4
[2025-07-04 11:17:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 11:19:57] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: python-requests/2.32.4
[2025-07-04 11:19:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: python-requests/2.32.4
[2025-07-04 11:20:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis | User-Agent: python-requests/2.32.4
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:42] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:21:58] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:05] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:14] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:15] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:22] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 11:22:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
