#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版应用 - 仅用于测试余额查询功能
"""

from flask import Flask, jsonify, request
import requests
import json

app = Flask(__name__)

@app.route('/')
def index():
    """主页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>余额查询测试</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>DeepSeek余额查询测试</h1>
        <button onclick="testBalance()">测试余额查询</button>
        <div id="result"></div>
        
        <script>
        async function testBalance() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在查询余额...';
            
            try {
                const response = await fetch('/api/deepseek/balance');
                const data = await response.json();
                
                if (data.success) {
                    const balance = data.balance;
                    if (balance.balance_infos && balance.balance_infos.length > 0) {
                        const balanceInfo = balance.balance_infos[0];
                        const totalBalance = balanceInfo.total_balance || '0.00';
                        resultDiv.innerHTML = `<h3>✅ 余额查询成功!</h3><p>当前余额: ${totalBalance}元</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    } else {
                        resultDiv.innerHTML = `<h3>⚠️ 余额信息格式异常</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    }
                } else {
                    resultDiv.innerHTML = `<h3>❌ 余额查询失败</h3><p>错误: ${data.error}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<h3>❌ 请求异常</h3><p>错误: ${error.message}</p>`;
            }
        }
        </script>
    </body>
    </html>
    """

@app.route('/api/deepseek/balance', methods=['GET'])
def get_deepseek_balance():
    """获取DeepSeek API余额"""
    try:
        print("🔍 开始查询DeepSeek余额...")
        
        # 直接使用固定的API密钥
        api_key = 'sk-b28e0b5d4412410db203c87809ccb9ad'
        
        if not api_key:
            print("❌ API密钥未配置")
            return jsonify({
                'success': False,
                'error': 'API密钥未配置'
            }), 400

        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {api_key}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        print(f"📡 发送余额查询请求...")
        
        # 添加SSL验证禁用和更长的超时时间，适配exe环境
        try:
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        except ImportError:
            pass  # urllib3不可用时忽略警告禁用
        
        response = requests.get(
            'https://api.deepseek.com/user/balance',
            headers=headers,
            timeout=60,  # 增加超时时间
            verify=False,  # 禁用SSL验证，解决exe打包后的证书问题
            allow_redirects=True
        )

        print(f"📊 余额查询响应状态: {response.status_code}")

        if response.status_code == 200:
            balance_data = response.json()
            print(f"✅ 余额查询成功: {balance_data}")
            return jsonify({
                'success': True,
                'balance': balance_data
            })
        else:
            error_msg = f'获取余额失败: HTTP {response.status_code}'
            try:
                error_detail = response.json()
                error_msg += f' - {error_detail}'
            except:
                error_msg += f' - {response.text[:200]}'
            
            print(f"❌ {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            }), response.status_code

    except requests.exceptions.Timeout:
        error_msg = '余额查询超时，请检查网络连接'
        print(f"⏰ {error_msg}")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 408
    except requests.exceptions.ConnectionError:
        error_msg = '无法连接到DeepSeek API，请检查网络连接'
        print(f"🌐 {error_msg}")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 503
    except Exception as e:
        error_msg = f'余额查询异常: {str(e)}'
        print(f"❌ {error_msg}")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 500

if __name__ == '__main__':
    print("🚀 启动简化版余额查询测试服务...")
    print("📍 访问地址: http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000)
