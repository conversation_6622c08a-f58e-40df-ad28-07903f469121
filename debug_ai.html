<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔧 AI分析功能调试</h1>
    
    <div class="section info">
        <h3>📋 测试步骤</h3>
        <button onclick="testStep1()">1. 测试AI配置获取</button>
        <button onclick="testStep2()">2. 测试分析数据获取</button>
        <button onclick="testStep3()">3. 测试提示构建</button>
        <button onclick="testStep4()">4. 测试DeepSeek API调用</button>
        <button onclick="testStep5()">5. 完整AI分析流程</button>
        <button onclick="clearLogs()">清空日志</button>
    </div>

    <div id="results" class="section">
        <h3>📊 测试结果</h3>
        <div id="logs" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logs.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // 测试1: AI配置获取
        async function testStep1() {
            log('🔍 测试AI配置获取...');
            try {
                const response = await fetch('/api/ai_config');
                const data = await response.json();
                log(`✅ AI配置获取成功: ${JSON.stringify(data, null, 2)}`, 'success');
                return data;
            } catch (error) {
                log(`❌ AI配置获取失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 测试2: 分析数据获取
        async function testStep2() {
            log('📊 测试分析数据获取...');
            try {
                const params = new URLSearchParams({
                    time_range: 'today',
                    station: 'EOL',
                    limit: 10
                });
                
                const response = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    const analysisData = data.data;
                    log(`✅ 分析数据获取成功:`, 'success');
                    log(`   - Top Issues: ${analysisData.top_issues.length}个`);
                    log(`   - 总测试数: ${analysisData.statistics.total_tests}`);
                    log(`   - 失败数: ${analysisData.statistics.failed_tests}`);
                    return analysisData;
                } else {
                    log(`❌ 分析数据获取失败: ${data.error}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ 分析数据获取异常: ${error.message}`, 'error');
                return null;
            }
        }

        // 测试3: 提示构建
        async function testStep3() {
            log('🤖 测试提示构建...');
            try {
                const analysisData = await testStep2();
                if (!analysisData) {
                    log('❌ 无法获取分析数据，跳过提示构建测试', 'error');
                    return null;
                }

                const prompt = buildAnalysisPrompt(analysisData);
                log(`✅ 提示构建成功，长度: ${prompt.length} 字符`, 'success');
                log(`提示预览: ${prompt.substring(0, 200)}...`);
                return prompt;
            } catch (error) {
                log(`❌ 提示构建失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 测试4: DeepSeek API调用
        async function testStep4() {
            log('🌐 测试DeepSeek API调用...');
            try {
                const config = await testStep1();
                const prompt = await testStep3();
                
                if (!config || !prompt) {
                    log('❌ 缺少配置或提示数据，跳过API调用测试', 'error');
                    return null;
                }

                log('🚀 开始调用DeepSeek API...');
                const result = await callDeepSeekAPI(config.config, prompt);
                log(`✅ DeepSeek API调用成功，结果长度: ${result.length} 字符`, 'success');
                log(`结果预览: ${result.substring(0, 200)}...`);
                return result;
            } catch (error) {
                log(`❌ DeepSeek API调用失败: ${error.message}`, 'error');
                console.error('API调用详细错误:', error);
                return null;
            }
        }

        // 测试5: 完整AI分析流程
        async function testStep5() {
            log('🎯 测试完整AI分析流程...');
            try {
                // 1. 获取配置
                const configResponse = await fetch('/api/ai_config');
                const configData = await configResponse.json();
                log('✅ 步骤1: 配置获取成功');

                // 2. 获取数据
                const params = new URLSearchParams({
                    time_range: 'today',
                    station: 'EOL',
                    limit: 10
                });
                
                const dataResponse = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                const analysisData = await dataResponse.json();
                
                if (!analysisData.success) {
                    throw new Error(analysisData.error || '获取分析数据失败');
                }
                log('✅ 步骤2: 分析数据获取成功');

                // 3. 构建提示
                const prompt = buildAnalysisPrompt(analysisData.data);
                log('✅ 步骤3: 提示构建成功');

                // 4. 调用AI
                const aiResult = await callDeepSeekAPI(configData.config, prompt);
                log('✅ 步骤4: AI调用成功');

                // 5. 解析结果
                const analysis = parseAIResponse(aiResult);
                log('✅ 步骤5: 结果解析成功');

                log('🎉 完整AI分析流程测试成功！', 'success');
                log(`最终分析结果: ${JSON.stringify(analysis, null, 2)}`);

            } catch (error) {
                log(`❌ 完整流程测试失败: ${error.message}`, 'error');
                console.error('完整流程错误:', error);
            }
        }

        // 构建分析提示（复制自主页面）
        function buildAnalysisPrompt(data) {
            console.log('构建分析提示，数据结构:', data);
            
            let top_issues, total_tests, failed_tests, time_info;
            
            if (data.statistics) {
                total_tests = parseInt(data.statistics.total_tests) || 0;
                failed_tests = parseInt(data.statistics.failed_tests) || 0;
                top_issues = data.top_issues || [];
                time_info = data.summary?.analysis_period || '未知时间范围';
            } else {
                total_tests = data.total_tests || 0;
                failed_tests = data.failed_tests || 0;
                top_issues = data.top_issues || [];
                time_info = data.time_info || '未知时间范围';
            }

            let prompt = `请分析以下汽车EOL测试数据：\\n\\n`;
            prompt += `时间范围: ${time_info}\\n`;
            prompt += `总测试数: ${total_tests}\\n`;
            prompt += `失败测试数: ${failed_tests}\\n`;
            
            if (total_tests > 0) {
                prompt += `失败率: ${((failed_tests / total_tests) * 100).toFixed(2)}%\\n\\n`;
            } else {
                prompt += `失败率: 0.00%\\n\\n`;
            }

            prompt += `Top失败项分布:\\n`;
            if (top_issues && top_issues.length > 0) {
                top_issues.forEach((issue, index) => {
                    const percentage = parseFloat(issue.percentage) || 0;
                    prompt += `${index + 1}. ${issue.failure_item}: ${issue.count}次 (${percentage.toFixed(2)}%)\\n`;
                });
            } else {
                prompt += `暂无失败项数据\\n`;
            }

            prompt += `\\n请提供专业的分析报告，包括：\\n`;
            prompt += `1. 数据概览和关键发现\\n`;
            prompt += `2. 失败趋势分析\\n`;
            prompt += `3. 可能的根本原因\\n`;
            prompt += `4. 优先级建议\\n`;
            prompt += `5. 改进措施建议\\n\\n`;
            prompt += `请用中文回答，格式清晰，重点突出。`;

            return prompt;
        }

        // 调用DeepSeek API（复制自主页面）
        async function callDeepSeekAPI(config, prompt) {
            console.log('调用DeepSeek API，配置:', config);
            console.log('提示内容长度:', prompt.length);
            
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer sk-b28e0b5d4412410db203c87809ccb9ad`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: config.model || 'deepseek-chat',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。'
                            },
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: config.max_tokens || 4000,
                        temperature: config.temperature || 0.3
                    })
                });

                console.log('DeepSeek API响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('DeepSeek API错误响应:', errorText);
                    throw new Error(`DeepSeek API调用失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('DeepSeek API响应数据:', data);
                
                if (data.choices && data.choices.length > 0) {
                    return data.choices[0].message.content;
                } else {
                    throw new Error('DeepSeek API返回数据格式异常');
                }
            } catch (error) {
                console.error('DeepSeek API调用异常:', error);
                throw error;
            }
        }

        // 解析AI响应（复制自主页面）
        function parseAIResponse(response) {
            const sections = {
                overview: '',
                trends: '',
                causes: '',
                priorities: '',
                improvements: ''
            };

            const lines = response.split('\\n');
            let currentSection = '';

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (trimmedLine.includes('数据概览') || trimmedLine.includes('关键发现')) {
                    currentSection = 'overview';
                } else if (trimmedLine.includes('失败趋势') || trimmedLine.includes('趋势分析')) {
                    currentSection = 'trends';
                } else if (trimmedLine.includes('根本原因') || trimmedLine.includes('原因分析')) {
                    currentSection = 'causes';
                } else if (trimmedLine.includes('优先级') || trimmedLine.includes('建议')) {
                    currentSection = 'priorities';
                } else if (trimmedLine.includes('改进措施') || trimmedLine.includes('改进建议')) {
                    currentSection = 'improvements';
                } else if (currentSection && trimmedLine) {
                    sections[currentSection] += trimmedLine + '\\n';
                }
            }

            return sections;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 AI分析调试页面已加载，可以开始测试');
        });
    </script>
</body>
</html>
