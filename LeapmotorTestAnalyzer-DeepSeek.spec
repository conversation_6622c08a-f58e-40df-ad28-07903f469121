# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[('static', 'static'), ('templates', 'templates'), ('version_info.json', '.'), ('ai_config.py', '.'), ('ai_analysis.py', '.')],
    hiddenimports=['pymysql', 'pandas', 'numpy', 'requests', 'ai_analysis', 'ai_config', 'flask', 'datetime', 'json', 'os', 're', 'traceback', 'urllib.parse', 'fnmatch'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='LeapmotorTestAnalyzer-DeepSeek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
