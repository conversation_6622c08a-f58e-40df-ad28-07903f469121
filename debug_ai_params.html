<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析参数调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .params {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .param-group {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .param-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .param-group select, .param-group input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 AI分析参数调试工具</h1>
        <p>用于调试AI分析时的参数传递问题</p>

        <div class="test-section">
            <h3>📊 测试参数设置</h3>
            <div class="params">
                <div class="param-group">
                    <label for="timeRange">时间范围:</label>
                    <select id="timeRange">
                        <option value="0">今天</option>
                        <option value="1">昨天</option>
                        <option value="7" selected>最近7天</option>
                        <option value="30">最近30天</option>
                    </select>
                </div>
                <div class="param-group">
                    <label for="station">测试站位:</label>
                    <select id="station">
                        <option value="">全部站位</option>
                        <option value="SOC">SOC</option>
                        <option value="EOL">EOL</option>
                    </select>
                </div>
                <div class="param-group">
                    <label for="slave">从站:</label>
                    <input type="text" id="slave" placeholder="留空表示全部">
                </div>
                <div class="param-group">
                    <label for="limit">Top问题数量:</label>
                    <select id="limit">
                        <option value="5">Top 5</option>
                        <option value="10" selected>Top 10</option>
                        <option value="15">Top 15</option>
                        <option value="20">Top 20</option>
                    </select>
                </div>
            </div>
            <button onclick="testDataAPI()">测试数据API</button>
            <button onclick="testAIPrompt()">测试AI提示构建</button>
            <button onclick="testFullAI()">测试完整AI分析</button>
        </div>

        <div class="test-section">
            <h3>📈 API数据测试结果</h3>
            <div id="dataResult" class="result info">点击"测试数据API"查看结果</div>
        </div>

        <div class="test-section">
            <h3>🤖 AI提示构建结果</h3>
            <div id="promptResult" class="result info">点击"测试AI提示构建"查看结果</div>
        </div>

        <div class="test-section">
            <h3>🧠 完整AI分析结果</h3>
            <div id="aiResult" class="result info">点击"测试完整AI分析"查看结果</div>
        </div>
    </div>

    <script>
        // 测试数据API
        async function testDataAPI() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在测试数据API...';

            try {
                const params = new URLSearchParams({
                    time_range: document.getElementById('timeRange').value,
                    station: document.getElementById('station').value,
                    slave: document.getElementById('slave').value,
                    limit: document.getElementById('limit').value
                });

                console.log('测试参数:', params.toString());

                const response = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    let result = `✅ API调用成功!\n\n`;
                    result += `📊 基础统计:\n`;
                    result += `  - 总测试数: ${data.data.total_tests}\n`;
                    result += `  - 失败测试数: ${data.data.failed_tests}\n`;
                    result += `  - 通过测试数: ${data.data.passed_tests}\n`;
                    result += `  - 失败率: ${data.data.fail_rate}%\n`;
                    result += `  - 时间范围: ${data.data.time_range}\n`;
                    result += `  - 站位: ${data.data.station || '全部'}\n\n`;
                    
                    result += `🔥 Top失败项 (${data.data.top_issues.length}个):\n`;
                    data.data.top_issues.forEach((issue, index) => {
                        const percentage = data.data.failed_tests > 0 ? 
                            (issue.count / data.data.failed_tests * 100).toFixed(2) : 0;
                        result += `  ${index + 1}. ${issue.failure_item}: ${issue.count}次 (${percentage}%)\n`;
                    });

                    resultDiv.className = 'result success';
                    resultDiv.textContent = result;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API调用失败: ${error.message}`;
                console.error('API调用错误:', error);
            }
        }

        // 测试AI提示构建
        async function testAIPrompt() {
            const resultDiv = document.getElementById('promptResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在构建AI提示...';

            try {
                // 先获取数据
                const params = new URLSearchParams({
                    time_range: document.getElementById('timeRange').value,
                    station: document.getElementById('station').value,
                    slave: document.getElementById('slave').value,
                    limit: document.getElementById('limit').value
                });

                const response = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                
                if (!response.ok) {
                    throw new Error(`数据获取失败: HTTP ${response.status}`);
                }

                const analysisData = await response.json();
                const data = analysisData.data;

                // 构建提示（模拟前端逻辑）
                const total_tests = parseInt(data.total_tests) || 0;
                const failed_tests = parseInt(data.failed_tests) || 0;
                const top_issues = data.top_issues || [];
                
                // 构建时间信息
                const timeRangeMap = {
                    '0': '今天',
                    '1': '昨天', 
                    '7': '最近7天',
                    '30': '最近30天'
                };
                let time_info = timeRangeMap[data.time_range] || `最近${data.time_range}天`;
                if (data.station) {
                    time_info += ` (站位: ${data.station})`;
                }

                let prompt = `请分析以下汽车EOL测试数据：\n\n`;
                prompt += `## 基础统计信息\n`;
                prompt += `时间范围: ${time_info}\n`;
                prompt += `总测试数: ${total_tests}\n`;
                prompt += `失败测试数: ${failed_tests}\n`;
                
                if (total_tests > 0) {
                    prompt += `失败率: ${((failed_tests / total_tests) * 100).toFixed(2)}%\n\n`;
                } else {
                    prompt += `失败率: 0.00%\n\n`;
                }
                
                prompt += `## Top失败项分布\n`;
                if (top_issues && top_issues.length > 0) {
                    top_issues.forEach((issue, index) => {
                        const count = parseInt(issue.count) || 0;
                        const percentage = failed_tests > 0 ? (count / failed_tests * 100) : 0;
                        prompt += `${index + 1}. ${issue.failure_item}: ${count}次 (${percentage.toFixed(2)}%)\n`;
                    });
                } else {
                    prompt += `暂无失败项数据\n`;
                }

                let result = `✅ AI提示构建成功!\n\n`;
                result += `📏 提示长度: ${prompt.length} 字符\n\n`;
                result += `📝 提示内容:\n`;
                result += `${'-'.repeat(50)}\n`;
                result += prompt;
                result += `${'-'.repeat(50)}\n`;

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 提示构建失败: ${error.message}`;
                console.error('提示构建错误:', error);
            }
        }

        // 测试完整AI分析
        async function testFullAI() {
            const resultDiv = document.getElementById('aiResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在进行完整AI分析...';

            try {
                // 1. 获取数据并构建提示
                const params = new URLSearchParams({
                    time_range: document.getElementById('timeRange').value,
                    station: document.getElementById('station').value,
                    slave: document.getElementById('slave').value,
                    limit: document.getElementById('limit').value
                });

                const response = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                
                if (!response.ok) {
                    throw new Error(`数据获取失败: HTTP ${response.status}`);
                }

                const analysisData = await response.json();
                const data = analysisData.data;

                // 构建提示
                const total_tests = parseInt(data.total_tests) || 0;
                const failed_tests = parseInt(data.failed_tests) || 0;
                const top_issues = data.top_issues || [];
                
                const timeRangeMap = {
                    '0': '今天',
                    '1': '昨天', 
                    '7': '最近7天',
                    '30': '最近30天'
                };
                let time_info = timeRangeMap[data.time_range] || `最近${data.time_range}天`;
                if (data.station) {
                    time_info += ` (站位: ${data.station})`;
                }

                let prompt = `请分析以下汽车EOL测试数据：\n\n`;
                prompt += `## 基础统计信息\n`;
                prompt += `时间范围: ${time_info}\n`;
                prompt += `总测试数: ${total_tests}\n`;
                prompt += `失败测试数: ${failed_tests}\n`;
                prompt += `失败率: ${total_tests > 0 ? ((failed_tests / total_tests) * 100).toFixed(2) : 0.00}%\n\n`;
                prompt += `## Top失败项分布\n`;
                
                if (top_issues && top_issues.length > 0) {
                    top_issues.forEach((issue, index) => {
                        const count = parseInt(issue.count) || 0;
                        const percentage = failed_tests > 0 ? (count / failed_tests * 100) : 0;
                        prompt += `${index + 1}. ${issue.failure_item}: ${count}次 (${percentage.toFixed(2)}%)\n`;
                    });
                }

                // 2. 调用AI API
                resultDiv.textContent += '\n🤖 正在调用DeepSeek API...';

                const aiResponse = await fetch('/api/deepseek/proxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。'
                            },
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: 4000,
                        temperature: 0.3
                    })
                });

                if (!aiResponse.ok) {
                    throw new Error(`AI调用失败: HTTP ${aiResponse.status}`);
                }

                const aiData = await aiResponse.json();
                const aiResult = aiData.choices[0].message.content;

                let result = `✅ 完整AI分析成功!\n\n`;
                result += `📊 数据统计:\n`;
                result += `  - 总测试数: ${total_tests}\n`;
                result += `  - 失败测试数: ${failed_tests}\n`;
                result += `  - 失败率: ${total_tests > 0 ? ((failed_tests / total_tests) * 100).toFixed(2) : 0.00}%\n`;
                result += `  - Top问题数: ${top_issues.length}\n\n`;
                result += `🤖 AI分析结果 (${aiResult.length} 字符):\n`;
                result += `${'-'.repeat(50)}\n`;
                result += aiResult;
                result += `\n${'-'.repeat(50)}\n`;

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 完整AI分析失败: ${error.message}`;
                console.error('完整AI分析错误:', error);
            }
        }

        // 页面加载时自动测试数据API
        window.onload = function() {
            testDataAPI();
        };
    </script>
</body>
</html>
