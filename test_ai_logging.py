#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析日志记录功能
"""

import requests
import json
import time

def test_ai_logging():
    """测试AI分析日志记录功能"""
    base_url = "http://localhost:5000"
    
    print("🧪 开始测试AI分析日志记录功能")
    print("=" * 50)
    
    # 测试1: 记录AI分析开始
    print("\n📝 测试1: 记录AI分析开始")
    try:
        response = requests.post(f"{base_url}/api/log_ai_analysis", 
                               json={
                                   "action": "ai_analysis_start",
                                   "timestamp": "2025-01-04T10:00:00Z",
                                   "parameters": {
                                       "time_range": "today",
                                       "station": "test_station",
                                       "limit": "10"
                                   }
                               })
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ 响应: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 测试2: 记录AI分析完成
    print("\n📝 测试2: 记录AI分析完成")
    try:
        response = requests.post(f"{base_url}/api/log_ai_analysis", 
                               json={
                                   "action": "ai_analysis_completed",
                                   "timestamp": "2025-01-04T10:01:00Z",
                                   "data_source": "客户端AI分析",
                                   "analysis_summary": "测试分析完成"
                               })
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ 响应: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 测试3: 记录AI分析失败
    print("\n📝 测试3: 记录AI分析失败")
    try:
        response = requests.post(f"{base_url}/api/log_ai_analysis", 
                               json={
                                   "action": "ai_analysis_failed",
                                   "timestamp": "2025-01-04T10:02:00Z",
                                   "error": "网络连接失败"
                               })
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ 响应: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 测试4: 查看访问日志
    print("\n📝 测试4: 查看访问日志")
    try:
        response = requests.get(f"{base_url}/api/access_log")
        print(f"✅ 状态码: {response.status_code}")
        logs = response.json()
        if isinstance(logs, list):
            print(f"✅ 日志条目数: {len(logs)}")
            # 显示最近的几条日志
            for i, log in enumerate(logs[:5]):
                print(f"  {i+1}. [{log.get('timestamp', 'N/A')}] {log.get('path', 'N/A')}")
        else:
            print(f"✅ 响应类型: {type(logs)}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")

def test_client_ai():
    """测试客户端AI调用"""
    print("\n🤖 开始测试客户端AI调用")
    print("=" * 50)
    
    # 测试DeepSeek余额查询
    print("\n💰 测试余额查询")
    try:
        response = requests.get("https://api.deepseek.com/user/balance", 
                              headers={
                                  "Authorization": "Bearer sk-b28e0b5d4412410db203c87809ccb9ad"
                              })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            balance = data['balance_infos'][0]['total_balance']
            currency = data['balance_infos'][0]['currency']
            print(f"✅ 余额: {balance} {currency}")
        else:
            print(f"❌ 错误: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试AI分析调用
    print("\n🧠 测试AI分析调用")
    try:
        response = requests.post("https://api.deepseek.com/v1/chat/completions",
                               headers={
                                   "Authorization": "Bearer sk-b28e0b5d4412410db203c87809ccb9ad",
                                   "Content-Type": "application/json"
                               },
                               json={
                                   "model": "deepseek-chat",
                                   "messages": [
                                       {
                                           "role": "user",
                                           "content": "请简单介绍汽车EOL测试的重要性，不超过100字。"
                                       }
                                   ],
                                   "max_tokens": 200,
                                   "temperature": 0.3
                               })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print(f"✅ AI响应: {content[:100]}...")
        else:
            print(f"❌ 错误: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试日志记录功能
    test_ai_logging()
    
    # 测试客户端AI调用
    test_client_ai()
