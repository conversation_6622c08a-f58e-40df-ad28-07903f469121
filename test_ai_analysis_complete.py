#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的AI分析流程
"""

import requests
import json
import time

def test_ai_analysis_data():
    """测试AI分析数据获取"""
    print("📊 测试AI分析数据获取")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:5000/api/lp8155/ai_analysis_data", 
                              params={
                                  'time_range': '7',
                                  'station': '',
                                  'slave': '',
                                  'limit': '5'
                              })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            print(f"✅ 数据获取成功:")
            print(f"  - 成功标志: {data.get('success')}")
            print(f"  - 总测试数: {data['data'].get('total_tests')}")
            print(f"  - 失败测试数: {data['data'].get('failed_tests')}")
            print(f"  - 失败率: {data['data'].get('fail_rate')}%")
            print(f"  - Top问题数量: {len(data['data'].get('top_issues', []))}")
            
            # 显示Top问题
            for i, issue in enumerate(data['data'].get('top_issues', [])[:3]):
                print(f"  - Top{i+1}: {issue['failure_item']} ({issue['count']}次)")
            
            return data['data']
        else:
            print(f"❌ 错误: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 异常: {e}")
        return None

def test_ai_analysis_prompt(data):
    """测试AI分析提示构建"""
    print("\n🤖 测试AI分析提示构建")
    print("=" * 50)
    
    try:
        # 模拟前端的buildAnalysisPrompt函数
        total_tests = int(data.get('total_tests', 0))
        failed_tests = int(data.get('failed_tests', 0))
        top_issues = data.get('top_issues', [])
        
        # 构建时间信息
        time_range_map = {
            '0': '今天',
            '1': '昨天', 
            '7': '最近7天',
            '30': '最近30天'
        }
        time_info = time_range_map.get(data.get('time_range'), f"最近{data.get('time_range')}天")
        if data.get('station'):
            time_info += f" (站位: {data['station']})"
        
        prompt = f"请分析以下汽车EOL测试数据：\n\n"
        prompt += f"## 基础统计信息\n"
        prompt += f"时间范围: {time_info}\n"
        prompt += f"总测试数: {total_tests}\n"
        prompt += f"失败测试数: {failed_tests}\n"
        
        if total_tests > 0:
            prompt += f"失败率: {((failed_tests / total_tests) * 100):.2f}%\n\n"
        else:
            prompt += f"失败率: 0.00%\n\n"
        
        prompt += f"## Top失败项分布\n"
        if top_issues:
            for index, issue in enumerate(top_issues):
                count = int(issue.get('count', 0))
                percentage = (count / failed_tests * 100) if failed_tests > 0 else 0
                prompt += f"{index + 1}. {issue['failure_item']}: {count}次 ({percentage:.2f}%)\n"
        
        print(f"✅ 提示构建成功，长度: {len(prompt)}")
        print(f"📝 提示内容预览:")
        print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
        
        return prompt
        
    except Exception as e:
        print(f"❌ 提示构建失败: {e}")
        return None

def test_deepseek_api(prompt):
    """测试DeepSeek API调用"""
    print("\n🌐 测试DeepSeek API调用")
    print("=" * 50)
    
    try:
        response = requests.post("https://api.deepseek.com/v1/chat/completions",
                               headers={
                                   "Authorization": "Bearer sk-b28e0b5d4412410db203c87809ccb9ad",
                                   "Content-Type": "application/json"
                               },
                               json={
                                   "model": "deepseek-chat",
                                   "messages": [
                                       {
                                           "role": "system",
                                           "content": "你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。"
                                       },
                                       {
                                           "role": "user",
                                           "content": prompt
                                       }
                                   ],
                                   "max_tokens": 4000,
                                   "temperature": 0.3
                               })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print(f"✅ AI分析成功，响应长度: {len(content)}")
            print(f"📝 AI分析结果预览:")
            print(content[:300] + "..." if len(content) > 300 else content)
            return content
        else:
            print(f"❌ 错误: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 异常: {e}")
        return None

def test_server_proxy(prompt):
    """测试服务器代理"""
    print("\n🔄 测试服务器代理")
    print("=" * 50)
    
    try:
        response = requests.post("http://localhost:5000/api/deepseek/proxy",
                               headers={
                                   "Content-Type": "application/json"
                               },
                               json={
                                   "model": "deepseek-chat",
                                   "messages": [
                                       {
                                           "role": "system",
                                           "content": "你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。"
                                       },
                                       {
                                           "role": "user",
                                           "content": prompt
                                       }
                                   ],
                                   "max_tokens": 4000,
                                   "temperature": 0.3
                               })
        print(f"✅ 状态码: {response.status_code}")
        if response.ok:
            data = response.json()
            content = data['choices'][0]['message']['content']
            print(f"✅ 代理调用成功，响应长度: {len(content)}")
            print(f"📝 代理分析结果预览:")
            print(content[:300] + "..." if len(content) > 300 else content)
            return content
        else:
            print(f"❌ 错误: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 异常: {e}")
        return None

def main():
    print("🚀 开始测试完整AI分析流程")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 1. 测试数据获取
    analysis_data = test_ai_analysis_data()
    if not analysis_data:
        print("❌ 数据获取失败，无法继续测试")
        return
    
    # 2. 测试提示构建
    prompt = test_ai_analysis_prompt(analysis_data)
    if not prompt:
        print("❌ 提示构建失败，无法继续测试")
        return
    
    # 3. 测试直接API调用
    direct_result = test_deepseek_api(prompt)
    
    # 4. 测试服务器代理
    proxy_result = test_server_proxy(prompt)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 完整流程测试结果总结:")
    print(f"  数据获取: ✅ 成功")
    print(f"  提示构建: ✅ 成功")
    print(f"  直接调用: {'✅ 成功' if direct_result else '❌ 失败'}")
    print(f"  服务器代理: {'✅ 成功' if proxy_result else '❌ 失败'}")
    
    if direct_result or proxy_result:
        print("\n🎉 AI分析功能完全正常！")
        print("💡 前端AI分析应该可以正常工作了")
    else:
        print("\n⚠️ AI调用仍有问题，需要进一步调试")

if __name__ == "__main__":
    main()
