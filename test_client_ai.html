<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户端AI调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 客户端AI调用测试</h1>
        <p>此页面用于测试在服务器无网络环境下的客户端AI调用功能</p>

        <div class="test-section info">
            <h3>📋 测试说明</h3>
            <p><strong>目标</strong>：验证客户端浏览器能够直接调用DeepSeek API，绕过服务器网络限制</p>
            <p><strong>场景</strong>：服务器部署在无外网环境，但客户端浏览器有网络连接</p>
            <p><strong>API密钥</strong>：sk-b28e0b5d4412410db203c87809ccb9ad</p>
        </div>

        <div class="test-section">
            <h3>🔍 测试1: 余额查询</h3>
            <button onclick="testBalance()">测试余额查询</button>
            <div id="balanceResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧠 测试2: AI分析调用</h3>
            <button onclick="testAICall()">测试AI分析</button>
            <div id="aiResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试3: 完整流程</h3>
            <button onclick="testFullFlow()">测试完整AI分析流程</button>
            <div id="fullFlowResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        const API_KEY = 'sk-b28e0b5d4412410db203c87809ccb9ad';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('testLog');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
            document.getElementById('balanceResult').textContent = '';
            document.getElementById('aiResult').textContent = '';
            document.getElementById('fullFlowResult').textContent = '';
        }

        // 测试1: 余额查询
        async function testBalance() {
            const resultDiv = document.getElementById('balanceResult');
            resultDiv.textContent = '正在查询余额...\n';
            
            try {
                log('开始测试余额查询');
                
                const response = await fetch('https://api.deepseek.com/user/balance', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Accept': 'application/json'
                    }
                });

                log(`余额查询响应状态: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    const balance = data.balance_infos[0].total_balance;
                    const currency = data.balance_infos[0].currency;
                    
                    resultDiv.textContent = `✅ 余额查询成功!\n当前余额: ${balance} ${currency}\n完整响应: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'log success';
                    log(`余额查询成功: ${balance} ${currency}`, 'success');
                } else {
                    const errorText = await response.text();
                    resultDiv.textContent = `❌ 余额查询失败\n状态码: ${response.status}\n错误: ${errorText}`;
                    resultDiv.className = 'log error';
                    log(`余额查询失败: ${response.status} ${errorText}`, 'error');
                }
            } catch (error) {
                resultDiv.textContent = `❌ 余额查询异常\n错误: ${error.message}`;
                resultDiv.className = 'log error';
                log(`余额查询异常: ${error.message}`, 'error');
            }
        }

        // 测试2: AI分析调用
        async function testAICall() {
            const resultDiv = document.getElementById('aiResult');
            resultDiv.textContent = '正在调用AI分析...\n';
            
            try {
                log('开始测试AI分析调用');
                
                const testPrompt = `请分析以下汽车测试数据：
测试项目: 电池电压测试
失败次数: 15次
总测试次数: 100次
失败率: 15%
主要失败原因: 电压偏低

请提供简短的分析建议。`;

                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的汽车测试数据分析专家。'
                            },
                            {
                                role: 'user',
                                content: testPrompt
                            }
                        ],
                        max_tokens: 500,
                        temperature: 0.3
                    })
                });

                log(`AI分析响应状态: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;
                    
                    resultDiv.textContent = `✅ AI分析成功!\n\n分析结果:\n${aiResponse}`;
                    resultDiv.className = 'log success';
                    log('AI分析调用成功', 'success');
                } else {
                    const errorText = await response.text();
                    resultDiv.textContent = `❌ AI分析失败\n状态码: ${response.status}\n错误: ${errorText}`;
                    resultDiv.className = 'log error';
                    log(`AI分析失败: ${response.status} ${errorText}`, 'error');
                }
            } catch (error) {
                resultDiv.textContent = `❌ AI分析异常\n错误: ${error.message}`;
                resultDiv.className = 'log error';
                log(`AI分析异常: ${error.message}`, 'error');
            }
        }

        // 测试3: 完整流程
        async function testFullFlow() {
            const resultDiv = document.getElementById('fullFlowResult');
            resultDiv.textContent = '正在执行完整流程测试...\n';
            
            try {
                log('开始完整流程测试');
                
                // 步骤1: 余额查询
                log('步骤1: 查询余额');
                const balanceResponse = await fetch('https://api.deepseek.com/user/balance', {
                    headers: { 'Authorization': `Bearer ${API_KEY}` }
                });
                
                if (!balanceResponse.ok) {
                    throw new Error(`余额查询失败: ${balanceResponse.status}`);
                }
                
                const balanceData = await balanceResponse.json();
                const balance = balanceData.balance_infos[0].total_balance;
                log(`✅ 余额查询成功: ${balance}元`);
                
                // 步骤2: AI分析
                log('步骤2: 执行AI分析');
                const aiResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [{
                            role: 'user',
                            content: '请简单介绍一下汽车EOL测试的重要性。'
                        }],
                        max_tokens: 200
                    })
                });
                
                if (!aiResponse.ok) {
                    throw new Error(`AI分析失败: ${aiResponse.status}`);
                }
                
                const aiData = await aiResponse.json();
                const analysis = aiData.choices[0].message.content;
                log('✅ AI分析成功');
                
                resultDiv.textContent = `✅ 完整流程测试成功!\n\n余额: ${balance}元\n\nAI分析结果:\n${analysis}`;
                resultDiv.className = 'log success';
                log('完整流程测试成功', 'success');
                
            } catch (error) {
                resultDiv.textContent = `❌ 完整流程测试失败\n错误: ${error.message}`;
                resultDiv.className = 'log error';
                log(`完整流程测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('客户端AI测试页面已加载');
            log('API密钥已配置，准备开始测试');
        };
    </script>
</body>
</html>
