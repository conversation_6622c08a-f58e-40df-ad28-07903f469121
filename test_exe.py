#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包的exe文件
"""

import subprocess
import time
import requests
import os

def test_exe():
    """测试exe文件是否正常工作"""
    print("🚀 开始测试打包的exe文件...")
    
    exe_path = os.path.join('dist', 'LeapmotorTestAnalyzer-DeepSeek.exe')
    
    if not os.path.exists(exe_path):
        print(f"❌ exe文件不存在: {exe_path}")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    print(f"📊 文件大小: {os.path.getsize(exe_path) / 1024 / 1024:.1f} MB")
    
    # 启动exe
    print("🔄 启动exe进程...")
    try:
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(10)
        
        # 测试web服务是否可访问
        print("🌐 测试web服务...")
        try:
            response = requests.get('http://localhost:5000', timeout=5)
            if response.status_code == 200:
                print("✅ Web服务正常运行")
                print(f"📄 响应长度: {len(response.text)} 字符")
                
                # 测试API端点
                print("🔍 测试API端点...")
                try:
                    api_response = requests.get('http://localhost:5000/api/ai_config', timeout=5)
                    if api_response.status_code == 200:
                        config_data = api_response.json()
                        print(f"✅ AI配置API正常: {config_data['config']['provider']}")
                    else:
                        print(f"⚠️ AI配置API响应异常: {api_response.status_code}")
                except Exception as e:
                    print(f"⚠️ AI配置API测试失败: {e}")
                
            else:
                print(f"❌ Web服务响应异常: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到web服务")
            return False
        except Exception as e:
            print(f"❌ Web服务测试失败: {e}")
            return False
        
        # 停止进程
        print("🛑 停止exe进程...")
        process.terminate()
        process.wait(timeout=5)
        
        print("🎉 exe文件测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ exe启动失败: {e}")
        return False

def show_package_info():
    """显示打包信息"""
    print("\n📦 打包信息:")
    print("=" * 50)
    
    dist_files = []
    if os.path.exists('dist'):
        for file in os.listdir('dist'):
            file_path = os.path.join('dist', file)
            if os.path.isfile(file_path):
                size_mb = os.path.getsize(file_path) / 1024 / 1024
                dist_files.append((file, size_mb))
    
    if dist_files:
        print("📁 dist目录文件:")
        for filename, size in dist_files:
            print(f"   {filename}: {size:.1f} MB")
    else:
        print("❌ dist目录为空或不存在")
    
    print("\n💡 使用说明:")
    print("1. 双击 'dist/启动系统.bat' 启动应用")
    print("2. 或直接运行 'dist/LeapmotorTestAnalyzer-DeepSeek.exe'")
    print("3. 在浏览器中访问 http://localhost:5000")
    print("4. AI分析功能已预配置DeepSeek API")

if __name__ == "__main__":
    success = test_exe()
    show_package_info()
    
    if success:
        print("\n✨ 打包测试完成，exe文件可以正常使用！")
    else:
        print("\n❌ 打包测试失败，请检查错误信息")
