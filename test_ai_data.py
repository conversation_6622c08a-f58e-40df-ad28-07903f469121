#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析数据
"""

import requests
import json

def test_ai_data():
    """测试AI分析数据获取和处理"""
    print("🔍 测试AI分析数据获取...")
    
    try:
        # 获取分析数据
        response = requests.get('http://localhost:5000/api/lp8155/ai_analysis_data?test_station=EOL&time_range=today')
        data = response.json()
        
        if data['success']:
            analysis_data = data['data']
            print("✅ 数据获取成功")
            print(f"数据结构: {list(analysis_data.keys())}")
            
            # 检查统计数据
            stats = analysis_data['statistics']
            print(f"\n📊 统计数据:")
            print(f"  总测试数: {stats['total_tests']}")
            print(f"  失败测试数: {stats['failed_tests']}")
            fail_rate = float(stats['fail_rate']) if stats['fail_rate'] is not None else 0.0
            print(f"  失败率: {fail_rate:.2f}%")
            
            # 检查Top Issues
            top_issues = analysis_data['top_issues']
            print(f"\n🔥 Top Issues ({len(top_issues)}个):")
            for i, issue in enumerate(top_issues[:5]):
                percentage = float(issue['percentage']) if issue['percentage'] is not None else 0.0
                print(f"  {i+1}. {issue['failure_item']}: {issue['count']}次 ({percentage:.2f}%)")
            
            # 构建AI提示
            print(f"\n🤖 构建AI提示:")
            prompt = build_ai_prompt(analysis_data)
            print(f"提示长度: {len(prompt)} 字符")
            print(f"提示预览:\n{prompt[:300]}...")
            
            return True
        else:
            print(f"❌ 数据获取失败: {data.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def build_ai_prompt(data):
    """构建AI分析提示"""
    stats = data['statistics']
    top_issues = data['top_issues']
    time_info = data['summary']['analysis_period']
    
    total_tests = int(stats['total_tests'])
    failed_tests = int(stats['failed_tests'])
    
    prompt = "请分析以下汽车EOL测试数据：\n\n"
    prompt += f"时间范围: {time_info}\n"
    prompt += f"总测试数: {total_tests}\n"
    prompt += f"失败测试数: {failed_tests}\n"
    
    if total_tests > 0:
        prompt += f"失败率: {((failed_tests / total_tests) * 100):.2f}%\n\n"
    else:
        prompt += "失败率: 0.00%\n\n"
    
    prompt += "Top失败项分布:\n"
    for i, issue in enumerate(top_issues[:10]):
        percentage = float(issue['percentage'])
        prompt += f"{i+1}. {issue['failure_item']}: {issue['count']}次 ({percentage:.2f}%)\n"
    
    prompt += "\n请提供专业的分析报告，包括：\n"
    prompt += "1. 数据概览和关键发现\n"
    prompt += "2. 失败趋势分析\n"
    prompt += "3. 可能的根本原因\n"
    prompt += "4. 优先级建议\n"
    prompt += "5. 改进措施建议\n\n"
    prompt += "请用中文回答，格式清晰，重点突出。"
    
    return prompt

def test_deepseek_api():
    """测试DeepSeek API调用"""
    print("\n🌐 测试DeepSeek API调用...")
    
    try:
        # 获取数据
        response = requests.get('http://localhost:5000/api/lp8155/ai_analysis_data?test_station=EOL&time_range=today')
        data = response.json()
        
        if not data['success']:
            print("❌ 无法获取分析数据")
            return False
        
        # 构建提示
        prompt = build_ai_prompt(data['data'])
        
        # 调用DeepSeek API
        api_response = requests.post('https://api.deepseek.com/v1/chat/completions', 
            headers={
                'Authorization': 'Bearer sk-b28e0b5d4412410db203c87809ccb9ad',
                'Content-Type': 'application/json'
            },
            json={
                'model': 'deepseek-chat',
                'messages': [
                    {
                        'role': 'system',
                        'content': '你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': 4000,
                'temperature': 0.3
            },
            timeout=60
        )
        
        print(f"API响应状态: {api_response.status_code}")
        
        if api_response.status_code == 200:
            result = api_response.json()
            if 'choices' in result and len(result['choices']) > 0:
                analysis = result['choices'][0]['message']['content']
                print(f"✅ AI分析成功")
                print(f"分析结果长度: {len(analysis)} 字符")
                print(f"分析预览:\n{analysis[:200]}...")
                return True
            else:
                print(f"❌ API响应格式异常: {result}")
                return False
        else:
            print(f"❌ API调用失败: {api_response.status_code}")
            print(f"错误信息: {api_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 AI分析数据和API测试")
    print("=" * 50)
    
    # 测试数据获取
    data_ok = test_ai_data()
    
    # 测试API调用
    if data_ok:
        api_ok = test_deepseek_api()
        
        if api_ok:
            print("\n🎉 所有测试通过！AI分析功能应该可以正常工作。")
            print("\n💡 如果前端还是不工作，可能的原因：")
            print("   1. 浏览器网络问题（CORS、防火墙等）")
            print("   2. JavaScript错误（查看浏览器控制台）")
            print("   3. 前端逻辑问题（数据处理、UI更新等）")
        else:
            print("\n❌ API调用失败，请检查网络连接和API密钥")
    else:
        print("\n❌ 数据获取失败，请检查数据库连接和查询逻辑")
    
    print("\n" + "=" * 50)

if __name__ == '__main__':
    main()
