#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试AI配置问题
"""

def test_ai_config_direct():
    """直接测试AI配置"""
    print("🔧 直接测试AI配置模块")
    print("=" * 40)
    
    try:
        from ai_config import AIConfig, AI_PROVIDERS
        
        # 创建配置实例
        ai_config = AIConfig()
        
        print("✅ AI配置模块导入成功")
        print(f"   Provider: {ai_config.get('provider')}")
        print(f"   Model: {ai_config.get('model')}")
        print(f"   Max Tokens: {ai_config.get('max_tokens')}")
        print(f"   Temperature: {ai_config.get('temperature')}")
        print(f"   Cache Enabled: {ai_config.get('cache_enabled')}")
        print(f"   Cache Duration: {ai_config.get('cache_duration_hours')}")
        print(f"   Client Side AI: {ai_config.get('client_side_ai')}")
        print(f"   Is Configured: {ai_config.is_configured()}")
        
        print(f"\n📋 可用提供商: {list(AI_PROVIDERS.keys())}")
        
        # 测试配置响应格式
        config_response = {
            'success': True,
            'config': {
                'provider': ai_config.get('provider'),
                'model': ai_config.get('model'),
                'max_tokens': ai_config.get('max_tokens'),
                'temperature': ai_config.get('temperature'),
                'cache_enabled': ai_config.get('cache_enabled'),
                'cache_duration_hours': ai_config.get('cache_duration_hours'),
                'client_side_ai': ai_config.get('client_side_ai'),
                'is_configured': ai_config.is_configured()
            },
            'providers': AI_PROVIDERS
        }
        
        print(f"\n📤 配置响应格式测试成功")
        print(f"   响应键: {list(config_response.keys())}")
        print(f"   配置键: {list(config_response['config'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_route():
    """测试Flask路由"""
    print("\n🌐 测试Flask路由")
    print("=" * 40)
    
    try:
        from flask import Flask
        from ai_config import AIConfig, AI_PROVIDERS
        
        app = Flask(__name__)
        
        @app.route('/test_ai_config')
        def test_ai_config_route():
            ai_config = AIConfig()
            
            return {
                'success': True,
                'config': {
                    'provider': ai_config.get('provider'),
                    'model': ai_config.get('model'),
                    'max_tokens': ai_config.get('max_tokens'),
                    'temperature': ai_config.get('temperature'),
                    'cache_enabled': ai_config.get('cache_enabled'),
                    'cache_duration_hours': ai_config.get('cache_duration_hours'),
                    'client_side_ai': ai_config.get('client_side_ai'),
                    'is_configured': ai_config.is_configured()
                },
                'providers': AI_PROVIDERS
            }
        
        # 测试路由
        with app.test_client() as client:
            response = client.get('/test_ai_config')
            
            if response.status_code == 200:
                print("✅ Flask路由测试成功")
                data = response.get_json()
                print(f"   状态码: {response.status_code}")
                print(f"   响应数据: {data['success']}")
                print(f"   配置提供商: {data['config']['provider']}")
                return True
            else:
                print(f"❌ Flask路由测试失败: {response.status_code}")
                print(f"   响应内容: {response.get_data(as_text=True)}")
                return False
                
    except Exception as e:
        print(f"❌ Flask路由测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🔍 AI配置调试工具")
    print("=" * 50)
    
    # 测试1: 直接测试AI配置
    success1 = test_ai_config_direct()
    
    # 测试2: 测试Flask路由
    success2 = test_flask_route()
    
    print(f"\n📊 测试结果")
    print("=" * 50)
    print(f"   AI配置模块: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"   Flask路由: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！AI配置应该可以正常工作。")
    else:
        print("\n⚠️  发现问题，需要进一步调试。")
