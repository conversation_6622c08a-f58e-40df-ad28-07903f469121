#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试余额查询功能
"""

import requests

def test_balance_direct():
    """直接测试余额查询"""
    try:
        print("🔍 直接测试DeepSeek余额查询...")
        
        api_key = '***********************************'
        
        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {api_key}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        print("📡 发送请求到DeepSeek API...")
        
        response = requests.get(
            'https://api.deepseek.com/user/balance',
            headers=headers,
            timeout=30,
            verify=False  # 禁用SSL验证
        )
        
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 余额查询成功!")
            print(f"📄 响应数据: {data}")
            
            if 'balance_infos' in data and data['balance_infos']:
                balance_info = data['balance_infos'][0]
                total_balance = balance_info.get('total_balance', '0.00')
                currency = balance_info.get('currency', 'CNY')
                print(f"💵 当前余额: {total_balance} {currency}")
                return True
            else:
                print("⚠️ 余额信息格式异常")
                return False
        else:
            print(f"❌ 余额查询失败: HTTP {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔍 测试DeepSeek余额查询功能 (直接调用)")
    print("=" * 50)
    
    success = test_balance_direct()
    
    if success:
        print("\n✅ 直接余额查询成功!")
        print("🎯 这说明API密钥和网络连接都正常")
        print("💡 exe中的余额查询问题可能已经修复")
    else:
        print("\n❌ 直接余额查询失败")
        print("🔧 需要检查API密钥或网络连接")
    
    print("\n" + "=" * 50)
    print("测试完成")
