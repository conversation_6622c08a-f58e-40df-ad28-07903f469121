#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek AI简化版 - 打包脚本
专门为简化的DeepSeek AI系统创建的打包工具
"""

import os
import sys
import subprocess
import shutil
import json
from datetime import datetime

def check_requirements():
    """检查必要的依赖"""
    print("🔍 检查打包环境...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查其他必要依赖
    required_packages = [
        'flask', 'pymysql', 'pandas', 'numpy', 'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请先安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def update_version():
    """更新版本信息"""
    print("📝 更新版本信息...")
    
    version_info = {
        "version": "2.0.0-DeepSeek",
        "release_date": datetime.now().strftime('%Y-%m-%d'),
        "build_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "build_timestamp": datetime.now().isoformat(),
        "ai_provider": "DeepSeek",
        "features": [
            "DeepSeek AI集成",
            "客户端AI调用",
            "余额查询",
            "收费提示",
            "简化配置"
        ]
    }
    
    with open('version_info.json', 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 版本信息已更新: {version_info['version']}")
    return version_info

def build_exe():
    """使用PyInstaller打包exe"""
    print("🔨 开始打包应用...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        print("🧹 清理旧的构建文件...")
        shutil.rmtree('dist')
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 检查必要文件是否存在
    required_files = ['static', 'ai_config.py', 'ai_analysis.py', 'app.py']
    missing_files = []

    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False

    # PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',  # 显示控制台，便于调试
        '--name=LeapmotorTestAnalyzer-DeepSeek',
        '--add-data=static;static',
        '--add-data=version_info.json;.',
        '--add-data=ai_config.py;.',
        '--add-data=ai_analysis.py;.',
        '--hidden-import=pymysql',
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=requests',
        '--hidden-import=ai_analysis',
        '--hidden-import=ai_config',
        '--hidden-import=flask',
        '--hidden-import=datetime',
        '--hidden-import=json',
        '--hidden-import=os',
        '--hidden-import=re',
        '--hidden-import=traceback',
        '--hidden-import=urllib.parse',
        '--hidden-import=fnmatch',
        'app.py'
    ]
    
    print(f"📦 执行命令: {' '.join(cmd[:5])}...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("📄 创建启动脚本...")
    
    batch_content = '''@echo off
chcp 65001 > nul
title Leapmotor Test Analysis System - DeepSeek AI

echo.
echo ========================================
echo   Leapmotor Test Analysis System - DeepSeek AI
echo ========================================
echo.
echo System Info:
echo   Version: 2.0.0-DeepSeek
echo   AI Provider: DeepSeek (Fixed Config)
echo   Port: 5000
echo   Client AI: Enabled by Default
echo.
echo Starting system...
echo.

cd /d "%~dp0"
LeapmotorTestAnalyzer-DeepSeek.exe

echo.
echo System closed
echo Press any key to exit...
pause > nul
'''

    with open('dist/启动系统.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 启动脚本已创建: dist/启动系统.bat")

def create_readme():
    """创建使用说明"""
    print("📖 创建使用说明...")
    
    readme_content = '''# 零跑测试数据分析系统 - DeepSeek AI简化版

## 🚀 快速启动

### 方法1: 使用启动脚本（推荐）
双击运行: `启动系统.bat`

### 方法2: 直接运行
双击运行: `LeapmotorTestAnalyzer-DeepSeek.exe`

### 方法3: 命令行运行
```cmd
LeapmotorTestAnalyzer-DeepSeek.exe
```

启动后在浏览器中访问: http://localhost:5000

## 🔧 系统配置

- **AI提供商**: DeepSeek AI (固定，无需配置)
- **API密钥**: *********************************** (预配置)
- **客户端AI**: 默认启用
- **端口**: 5000
- **版本**: 2.0.0-DeepSeek

## 💰 AI分析费用说明

- AI分析功能使用DeepSeek API，会产生少量费用
- 每次分析约消耗 $0.001-0.01
- 系统会在分析前显示余额和费用确认对话框
- 支持余额查询功能

## 📋 主要功能

1. **测试数据查询**: 支持多种筛选条件和时间范围
2. **访问日志管理**: 500条/页，支持排序和刷新
3. **AI智能分析**: DeepSeek驱动的数据分析和报告生成
4. **版本信息**: 显示构建时间和版本号
5. **余额查询**: 实时查询DeepSeek API余额
6. **收费提示**: 使用前显示费用确认对话框

## 🌐 网络配置

### 客户端AI调用（推荐）
- 默认启用，AI请求从浏览器发起
- 适用于服务器网络受限的环境
- 绕过服务器防火墙限制

### 服务器端AI调用
- 如果服务器网络正常，可以禁用客户端模式
- 在AI配置中取消勾选"客户端网络AI调用"

## 🔍 故障排除

### 端口占用问题
如果5000端口被占用：
1. 关闭占用端口的其他程序
2. 或修改app.py中的端口配置

### 数据库连接问题
确保MySQL数据库服务正常运行并检查连接配置

### AI分析失败
1. 检查网络连接
2. 启用"客户端网络AI调用"
3. 确认API密钥余额充足

### 客户端AI调用失败
1. 检查浏览器网络连接
2. 确认防火墙设置
3. 尝试刷新页面重新分析

## 📞 技术支持

如有问题，请联系开发团队。

---
构建时间: {build_time}
版本: 2.0.0-DeepSeek AI简化版
特性: DeepSeek集成、客户端AI、余额查询、收费提示
'''.format(build_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建: dist/使用说明.txt")

def main():
    """主函数"""
    print("🎯 零跑测试数据分析系统 - DeepSeek AI简化版打包工具")
    print("=" * 60)
    
    # 检查环境
    if not check_requirements():
        print("❌ 环境检查失败，请安装必要依赖后重试")
        input("按回车键退出...")
        return False
    
    # 更新版本信息
    version_info = update_version()
    
    # 执行打包
    if not build_exe():
        print("❌ 打包失败")
        input("按回车键退出...")
        return False
    
    # 创建辅助文件
    create_startup_script()
    create_readme()
    
    print("\n🎉 打包完成！")
    print("=" * 60)
    print("📁 输出目录: dist/")
    print("🚀 可执行文件: dist/LeapmotorTestAnalyzer-DeepSeek.exe")
    print("📄 启动脚本: dist/启动系统.bat")
    print("📖 使用说明: dist/使用说明.txt")
    print(f"📦 版本: {version_info['version']}")
    print("\n💡 使用说明:")
    print("   1. 双击'启动系统.bat'启动应用")
    print("   2. 在浏览器中访问 http://localhost:5000")
    print("   3. AI分析功能已预配置DeepSeek API")
    print("   4. 支持客户端AI调用和余额查询")
    
    input("\n✨ 打包成功完成！按回车键退出...")
    return True

if __name__ == '__main__':
    main()
