#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析功能完整演示脚本
"""

import requests
import json
import time

def demo_ai_analysis_workflow():
    """演示完整的AI分析工作流程"""
    print("🚀 零跑汽车测试数据AI分析系统演示")
    print("=" * 60)
    
    # 1. 检查AI配置状态
    print("\n📋 步骤1: 检查AI配置状态")
    print("-" * 30)
    
    try:
        response = requests.get('http://localhost:5000/api/ai_config')
        if response.status_code == 200:
            config_data = response.json()
            config = config_data['config']
            
            print(f"✅ 配置状态: {'已配置' if config['is_configured'] else '未配置（使用演示模式）'}")
            print(f"📡 AI提供商: {config['provider']}")
            print(f"🤖 模型: {config['model']}")
            print(f"🎛️ 最大Token: {config['max_tokens']}")
            print(f"🌡️ 温度参数: {config['temperature']}")
            print(f"💾 缓存状态: {'启用' if config['cache_enabled'] else '禁用'}")
            
            if config['cache_enabled']:
                print(f"⏰ 缓存时长: {config['cache_duration_hours']}小时")
        else:
            print("❌ 无法获取配置信息")
            return
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return
    
    # 2. 演示AI分析功能
    print("\n🧠 步骤2: 执行AI分析")
    print("-" * 30)
    
    # 测试不同的分析参数
    test_scenarios = [
        {
            'name': '最近7天全站分析',
            'params': {'time_range': '7', 'station': '', 'slave': '', 'limit': '10'}
        },
        {
            'name': '最近30天分析（前5项）',
            'params': {'time_range': '30', 'station': '', 'slave': '', 'limit': '5'}
        },
        {
            'name': '今日分析',
            'params': {'time_range': '0', 'station': '', 'slave': '', 'limit': '8'}
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 场景{i}: {scenario['name']}")
        print("   参数:", scenario['params'])
        
        try:
            start_time = time.time()
            response = requests.get(
                'http://localhost:5000/api/lp8155/ai_analysis',
                params=scenario['params']
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    analysis = data['analysis']
                    
                    print(f"   ✅ 分析成功 (耗时: {end_time - start_time:.2f}秒)")
                    print(f"   📊 数据来源: {data.get('data_source', '测试数据库')}")
                    print(f"   🎯 分析置信度: {analysis['data_insights']['analysis_confidence']}")
                    print(f"   📈 分析的问题数: {analysis['data_insights']['total_issues_analyzed']}")
                    
                    # 显示分析摘要
                    print(f"   📝 分析摘要:")
                    summary_lines = analysis['summary'].split('。')[:2]  # 只显示前两句
                    for line in summary_lines:
                        if line.strip():
                            print(f"      • {line.strip()}。")
                    
                    # 显示关键发现
                    print(f"   🔍 关键发现 ({len(analysis['key_findings'])}项):")
                    for j, finding in enumerate(analysis['key_findings'][:3], 1):  # 只显示前3项
                        print(f"      {j}. {finding}")
                    
                    # 显示优先级建议
                    if analysis.get('priority_recommendations'):
                        print(f"   ⚡ 优先建议:")
                        for j, rec in enumerate(analysis['priority_recommendations'][:2], 1):  # 只显示前2项
                            print(f"      {j}. {rec}")
                    
                else:
                    print(f"   ❌ 分析失败: {data.get('error', '未知错误')}")
            else:
                print(f"   ❌ 请求失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 分析异常: {e}")
        
        # 在场景之间稍作停顿
        if i < len(test_scenarios):
            time.sleep(1)
    
    # 3. 演示缓存功能
    print(f"\n💾 步骤3: 演示缓存功能")
    print("-" * 30)
    
    print("🔄 第一次请求（无缓存）:")
    start_time = time.time()
    response1 = requests.get(
        'http://localhost:5000/api/lp8155/ai_analysis',
        params={'time_range': '7', 'limit': '5'}
    )
    time1 = time.time() - start_time
    print(f"   耗时: {time1:.2f}秒")
    
    print("🚀 第二次请求（使用缓存）:")
    start_time = time.time()
    response2 = requests.get(
        'http://localhost:5000/api/lp8155/ai_analysis',
        params={'time_range': '7', 'limit': '5'}
    )
    time2 = time.time() - start_time
    print(f"   耗时: {time2:.2f}秒")
    
    if time2 < time1 * 0.5:  # 如果第二次请求明显更快
        print("   ✅ 缓存生效！第二次请求明显更快")
    else:
        print("   ℹ️ 缓存可能未生效或数据已更新")
    
    # 4. 总结
    print(f"\n🎉 演示完成")
    print("=" * 60)
    print("✨ AI分析系统功能特点:")
    print("   • 支持多种时间范围的数据分析")
    print("   • 智能识别Top Issue分布和趋势")
    print("   • 提供专业的根因分析和改进建议")
    print("   • 内置缓存机制，提升响应速度")
    print("   • 支持多种AI服务提供商")
    print("   • 完善的错误处理和重试机制")
    print("\n🔧 使用建议:")
    print("   • 配置真实的AI API密钥以获得更准确的分析")
    print("   • 根据数据量调整分析参数和缓存时长")
    print("   • 定期查看AI分析结果，持续改进测试质量")

if __name__ == '__main__':
    demo_ai_analysis_workflow()
