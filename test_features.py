#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能测试脚本
测试新实现的功能是否正常工作
"""

import json
import time
from app import load_version_info

def test_version_management():
    """测试版本管理功能"""
    print("=== 测试版本管理功能 ===")
    
    # 第一次加载版本信息
    print("第一次加载版本信息:")
    version1 = load_version_info()
    print(f"  版本号: {version1['version']}")
    print(f"  发布日期: {version1['release_date']}")
    print(f"  服务启动: {version1['service_start_time']}")
    
    # 等待1秒
    time.sleep(1)
    
    # 第二次加载版本信息
    print("\n第二次加载版本信息（1秒后）:")
    version2 = load_version_info()
    print(f"  版本号: {version2['version']}")
    print(f"  发布日期: {version2['release_date']}")
    print(f"  服务启动: {version2['service_start_time']}")
    
    # 验证结果
    print("\n验证结果:")
    if version1['version'] == version2['version']:
        print("✓ 版本号保持不变（正确）")
    else:
        print("✗ 版本号发生变化（错误）")
    
    if version1['release_date'] == version2['release_date']:
        print("✓ 发布日期保持不变（正确）")
    else:
        print("✗ 发布日期发生变化（错误）")
    
    if version1['service_start_time'] != version2['service_start_time']:
        print("✓ 服务启动时间更新（正确）")
    else:
        print("✗ 服务启动时间未更新（错误）")

def test_access_log_settings():
    """测试访问日志设置"""
    print("\n=== 测试访问日志设置 ===")
    
    # 读取HTML文件检查默认分页设置
    try:
        with open('static/index.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查分页默认值
        if 'logPageSize = 500' in content:
            print("✓ 访问日志默认分页设置为500条/页（正确）")
        else:
            print("✗ 访问日志默认分页设置不正确")
        
        # 检查刷新按钮
        if 'id="refreshAccessLog"' in content and '刷新' in content:
            print("✓ 访问日志刷新按钮已添加（正确）")
        else:
            print("✗ 访问日志刷新按钮未找到")
        
        # 检查排序功能
        if 'data-sort=' in content and 'fa-sort' in content:
            print("✓ 访问日志表格排序功能存在（正确）")
        else:
            print("✗ 访问日志表格排序功能未找到")
            
    except Exception as e:
        print(f"✗ 读取HTML文件失败: {e}")

def main():
    """主测试函数"""
    print("零跑项目功能测试")
    print("=" * 50)
    
    test_version_management()
    test_access_log_settings()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == '__main__':
    main()
