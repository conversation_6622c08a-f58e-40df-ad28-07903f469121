<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据分析系统</title>
    <!-- 网页图标 - 测试系统主题 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8IS0tIEJhY2tncm91bmQgLS0+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTUiIGZpbGw9InVybCgjZ3JhZGllbnQwKSIgc3Ryb2tlPSIjMDA3OGQ0IiBzdHJva2Utd2lkdGg9IjIiLz4KICA8IS0tIEdyYWRpZW50IC0tPgogIDxkZWZzPgogICAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudDAiIHgxPSIwIiB5MT0iMCIgeDI9IjMyIiB5Mj0iMzIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KICAgICAgPHN0b3Agc3RvcC1jb2xvcj0iIzRkYTZmZiIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiMwMDc4ZDQiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDwhLS0gVGVzdCBUdWJlIC0tPgogIDxyZWN0IHg9IjEwIiB5PSI4IiB3aWR0aD0iNCIgaGVpZ2h0PSIxMiIgcng9IjIiIGZpbGw9IiNmZmZmZmYiIHN0cm9rZT0iIzMzNzNkYyIgc3Ryb2tlLXdpZHRoPSIxIi8+CiAgPCEtLSBUZXN0IExpbmVzIC0tPgogIDxsaW5lIHgxPSI2IiB5MT0iMTAiIHgyPSIxMCIgeTI9IjEwIiBzdHJva2U9IiMzMzczZGMiIHN0cm9rZS13aWR0aD0iMiIvPgogIDxsaW5lIHgxPSI2IiB5MT0iMTQiIHgyPSIxMCIgeTI9IjE0IiBzdHJva2U9IiMzMzczZGMiIHN0cm9rZS13aWR0aD0iMiIvPgogIDxsaW5lIHgxPSI2IiB5MT0iMTgiIHgyPSIxMCIgeTI9IjE4IiBzdHJva2U9IiMzMzczZGMiIHN0cm9rZS13aWR0aD0iMiIvPgogIDwhLS0gQ2hlY2ttYXJrIC0tPgogIDxwYXRoIGQ9Ik0xOCA5TDIwIDExTDI0IDciIHN0cm9rZT0iIzEwYjk4MSIgc3Ryb2tlLXdpZHRoPSIyLjUiIGZpbGw9Im5vbmUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgogIDwhLS0gRGF0YSBQb2ludHMgLS0+CiAgPGNpcmNsZSBjeD0iMTgiIGN5PSIxNiIgcj0iMS41IiBmaWxsPSIjZmY2YjM1Ii8+CiAgPGNpcmNsZSBjeD0iMjIiIGN5PSIxOCIgcj0iMS41IiBmaWxsPSIjZmY2YjM1Ii8+CiAgPGNpcmNsZSBjeD0iMjAiIGN5PSIyMiIgcj0iMS41IiBmaWxsPSIjZmY2YjM1Ii8+CiAgPCEtLSBDb25uZWN0aW5nIExpbmVzIC0tPgogIDxsaW5lIHgxPSIxOCIgeTE9IjE2IiB4Mj0iMjIiIHkyPSIxOCIgc3Ryb2tlPSIjZmY2YjM1IiBzdHJva2Utd2lkdGg9IjEuNSIvPgogIDxsaW5lIHgxPSIyMiIgeTE9IjE4IiB4Mj0iMjAiIHkyPSIyMiIgc3Ryb2tlPSIjZmY2YjM1IiBzdHJva2Utd2lkdGg9IjEuNSIvPgo8L3N2Zz4K">
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/daterangepicker.css" rel="stylesheet">
    <style>
        .chart-container {
            height: 300px;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .sortable {
            cursor: pointer;
            user-select: none;
        }
        .sortable:hover {
            background-color: #f8f9fa;
        }
        .sort-icon::after {
            content: "↕️";
            margin-left: 5px;
            font-size: 12px;
        }
        .sort-asc::after {
            content: "↑";
        }
        .sort-desc::after {
            content: "↓";
        }
        .result-pass {
            color: #16a34a !important;
            font-weight: bold;
        }
        .result-fail {
            color: #dc2626 !important;
            font-weight: bold;
        }
        .log-table tr:nth-child(even) {
            background-color: transparent;
        }
        .log-table tr:nth-child(odd) {
            background-color: transparent;
        }
        .modal-xl {
            max-width: 90%;
        }
        .log-table {
            width: 100%;
        }
        .log-table th {
            position: sticky;
            top: 0;
            background: white;
            z-index: 1;
        }
        .log-path {
            word-break: break-all;
        }
        .modal-body {
            max-height: 80vh;
            overflow-y: auto;
        }
        .table th {
            white-space: nowrap;
        }
        .table td {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .table td:hover {
            white-space: normal;
            word-break: break-all;
        }
        .test-item-link {
            color: #0d6efd;
            cursor: pointer;
            text-decoration: underline;
        }
        .test-item-link:hover {
            color: #0a58ca;
        }
        .main-title {
            font-size: 2.5rem;
            font-weight: bold;
            letter-spacing: 2px;
            margin-bottom: 2rem;
        }
        .nav-tabs .nav-link.active {
            color: #1976d2 !important;
            border-color: #1976d2 #1976d2 #fff;
            background-color: #fff;
        }
        .nav-tabs .nav-link {
            color: #222 !important;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
        }

        /* 神秘入口样式 */
        .secret-entrance {
            position: fixed;
            top: 10px;
            left: 10px;
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            z-index: 10000;
            opacity: 0.7;
            transition: all 0.3s ease;
            animation: gradientShift 3s ease infinite;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .secret-entrance:hover {
            opacity: 1;
            transform: scale(1.1);
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
        }

        .secret-entrance::before {
            content: "✨";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
        }

        /* 烟花模态框样式 */
        .fireworks-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 9999;
            overflow: hidden;
        }

        .fireworks-modal.active {
            display: block;
        }

        .fireworks-close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.5);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 10001;
            font-family: "Russo One", arial, sans-serif;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .fireworks-close-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.8);
        }

        .fireworks-tips {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: rgba(255,255,255,0.8);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            text-align: center;
            z-index: 10001;
            font-family: "Russo One", arial, sans-serif;
            letter-spacing: 0.5px;
            animation: tipsFadeIn 2s ease-in-out;
        }

        @keyframes tipsFadeIn {
            0% { opacity: 0; transform: translateX(-50%) translateY(20px); }
            100% { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* 烟花容器样式 */
        .fireworks-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .fireworks-container .loading-init {
            color: rgba(255, 255, 255, 0.5);
            font-family: "Russo One", arial, sans-serif;
            text-align: center;
            text-transform: uppercase;
        }

        .fireworks-container .loading-init__header {
            font-size: 2.2em;
        }

        .fireworks-container .loading-init__status {
            margin-top: 1em;
            font-size: 0.8em;
            opacity: 0.75;
        }

        .fireworks-container .stage-container {
            overflow: hidden;
            box-sizing: initial;
            border: 1px solid #222;
            margin: -1px;
            width: 100%;
            height: 100%;
        }

        .fireworks-container .canvas-container {
            width: 100%;
            height: 100%;
            transition: filter 0.3s;
        }

        .fireworks-container .canvas-container canvas {
            position: absolute;
            mix-blend-mode: lighten;
            transform: translateZ(0);
        }

        .fireworks-container .controls {
            position: absolute;
            top: 0;
            width: 100%;
            padding-bottom: 50px;
            display: flex;
            justify-content: space-between;
            transition: opacity 0.3s, visibility 0.3s;
            z-index: 10002;
        }

        .fireworks-container .btn {
            opacity: 0.16;
            width: 50px;
            height: 50px;
            display: flex;
            user-select: none;
            cursor: default;
            transition: opacity 0.3s;
        }

        .fireworks-container .btn:hover {
            opacity: 0.32;
        }

        .fireworks-container .btn svg {
            display: block;
            margin: auto;
        }

        .fireworks-container .remove {
            display: none !important;
        }
        
        /* 添加固定列宽样式 */
        #fullLogModalContent .table th:nth-child(1) { width: 5%; }  /* 序号 */
        #fullLogModalContent .table th:nth-child(2) { width: 15%; } /* Test Seq */
        #fullLogModalContent .table th:nth-child(3) { width: 30%; } /* step_name */
        #fullLogModalContent .table th:nth-child(4) { width: 8%; }  /* StandardValue */
        #fullLogModalContent .table th:nth-child(5) { width: 8%; }  /* MeasuredValue */
        #fullLogModalContent .table th:nth-child(6) { width: 7%; }  /* LowLimit */
        #fullLogModalContent .table th:nth-child(7) { width: 7%; }  /* HighLimit */
        #fullLogModalContent .table th:nth-child(8) { width: 20%; } /* Result */
        
        #fullLogModalContent .table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        #fullLogModalContent .table td:hover {
            white-space: normal;
            overflow: visible;
            position: relative;
            z-index: 1;
        }

        /* 版本徽章样式 */
        .version-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
        }

        .version-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .version-badge i {
            margin-right: 5px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 版本详情弹窗样式 */
        .version-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .version-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            text-align: center;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .version-content h3 {
            color: #667eea;
            margin-bottom: 20px;
        }

        .version-info {
            text-align: left;
            margin: 20px 0;
        }

        .version-info .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .version-info .info-item:last-child {
            border-bottom: none;
        }

        .close-version-modal {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .close-version-modal:hover {
            background: #764ba2;
        }

        /* 访问日志表格样式 */
        #accessLogTable {
            font-size: 0.9rem;
        }

        #accessLogTable th {
            background-color: #343a40 !important;
            color: white !important;
            font-weight: 600;
            border: none;
            user-select: none;
        }

        #accessLogTable th:hover {
            background-color: #495057 !important;
        }

        #accessLogTable th i {
            margin-left: 5px;
            opacity: 0.7;
        }

        #accessLogTable tbody tr:hover {
            background-color: #f8f9fa;
        }

        #accessLogTable .badge {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .table-responsive {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }

        .log-filter-section {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .pagination-sm .page-link {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        #recordCount {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 神秘入口按钮 -->
    <button class="secret-entrance" id="secretEntrance" title="点击观赏烟花"></button>

    <!-- 烟花模态框 -->
    <div class="fireworks-modal" id="fireworksModal">
        <button class="fireworks-close-btn" id="fireworksCloseBtn">关闭烟花 (ESC)</button>
        <div class="fireworks-tips">
            享受美丽的烟花效果 | 按ESC键退出
        </div>
        <div class="fireworks-container">
            <iframe id="fireworks-iframe"
                    width="100%"
                    height="100%"
                    src="/static/fireworks.html"
                    allowfullscreen="allowfullscreen"
                    frameborder="0"
                    style="border: none; background: #000; width: 100vw; height: 100vh;">
            </iframe>
        </div>
    </div>

    <!-- 版本详情弹窗 -->
    <div class="version-modal" id="versionModal">
        <div class="version-content">
            <h3><i class="fas fa-info-circle"></i> 系统版本信息</h3>
            <div class="version-info">
                <div class="info-item">
                    <span><strong>当前版本:</strong></span>
                    <span id="currentVersion">v1.0.0</span>
                </div>
                <div class="info-item">
                    <span><strong>发布日期:</strong></span>
                    <span id="releaseDate">2024-01-01</span>
                </div>
                <div class="info-item">
                    <span><strong>服务启动:</strong></span>
                    <span id="serviceStartTime">2024-01-01 12:00:00</span>
                </div>
                <div class="info-item">
                    <span><strong>系统名称:</strong></span>
                    <span>测试数据分析系统</span>
                </div>
                <div class="info-item">
                    <span><strong>开发者:</strong></span>
                    <span>IE：陶茂源</span>
                </div>
            </div>
            <button class="close-version-modal" onclick="closeVersionModal()">关闭</button>
        </div>
    </div>

    <!-- SVG图标定义 -->
    <div style="height: 0; width: 0; position: absolute; visibility: hidden;">
        <svg xmlns="http://www.w3.org/2000/svg">
            <symbol id="icon-play" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
            </symbol>
            <symbol id="icon-pause" viewBox="0 0 24 24">
                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
            </symbol>
            <symbol id="icon-close" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>
            </symbol>
            <symbol id="icon-sound-on" viewBox="0 0 24 24">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
            </symbol>
            <symbol id="icon-sound-off" viewBox="0 0 24 24">
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
            </symbol>
        </svg>
    </div>

    <!-- 版本显示 -->
    <div class="position-fixed top-0 end-0 m-3" style="z-index: 1000;">
        <div class="version-badge" id="versionBadge">
            <i class="fas fa-code-branch"></i>
            <span id="versionText">v1.0.0</span>
        </div>
    </div>

    <div class="container mt-4">
        <div class="text-center mb-4">
            <h1 class="main-title mb-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: bold; font-size: 2.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.1);">
                测试数据分析系统-By <span style="position:relative;">IE<span id="hiddenLogTrigger" title="秘密入口" style="position:absolute; top:0; left:0; width:100%; height:100%; cursor:pointer; z-index: 10;"></span></span>
            </h1>
        </div>
        <div class="card mb-4">
            <div class="card-body p-0">
                <ul class="nav nav-tabs" id="mainTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="vw-tab" data-bs-toggle="tab" data-bs-target="#vw-panel" type="button" role="tab">大众项目</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="leap-tab" data-bs-toggle="tab" data-bs-target="#leap-panel" type="button" role="tab">零跑项目</button>
                    </li>
                </ul>
            </div>
        </div>
        <div class="tab-content" id="mainTabContent">
            <div class="tab-pane fade show active" id="vw-panel" role="tabpanel">
                <div class="container mt-4">
                    <!-- <h2 class="text-center mb-4">测试数据分析系统-By IE</h2> -->
                    
                    <!-- 查询条件卡片 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>时间范围</label>
                                        <select class="form-select" id="timeRange">
                                            <option value="0" selected>今天</option>
                                            <option value="1">昨天</option>
                                            <option value="7">最近7天</option>
                                            <option value="30">最近30天</option>
                                            <option value="custom">自定义时间范围</option>
                                        </select>
                                    </div>
                                </div>

                                <div id="customDateInputs" class="col-md-6" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>开始时间</label>
                                                <input type="datetime-local" class="form-control" id="customDateStart">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>结束时间</label>
                                                <input type="datetime-local" class="form-control" id="customDateEnd">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>站位</label>
                                        <select class="form-select" id="stationSelect">
                                            <option value="AP5">AP5</option>
                                            <option value="AP6">AP6</option>
                                        </select>
                                    </div>
                                </div>

                                <div id="slaveSelectContainer" class="col-md-2">
                                    <div class="form-group">
                                        <label>Slave</label>
                                        <select class="form-select" id="slaveSelect">
                                            <option value="">全部</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>测试结果</label>
                                        <select class="form-select" id="resultFilter">
                                            <option value="">全部</option>
                                            <option value="PASSED">Pass</option>
                                            <option value="FAILED">Fail</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-1 d-flex align-items-end">
                                    <button class="btn btn-primary" id="queryBtn">
                                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                        查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试总览和CT统计行 -->
                    <div class="row mt-4">
                        <!-- 测试总览卡片 -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">测试总览</div>
                                <div class="card-body">
                                    <div class="test-stats">
                                        <h4 class="mb-3">总测试数: <span id="totalTests">0</span></h4>
                                        <div class="row justify-content-between">
                                            <div class="col-4 text-center">
                                                <div class="pass-count text-success">
                                                    通过: <span id="passCount">0</span>
                                                </div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="fail-count text-danger">
                                                    失败: <span id="failCount">0</span>
                                                </div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="pass-rate text-primary" 
                                                     data-bs-toggle="tooltip" 
                                                     data-bs-html="true"
                                                     data-bs-placement="bottom" 
                                                     id="fpyTooltip">
                                                    FPY: <span id="passRate">0</span>%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- CT统计卡片 -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">CT统计</div>
                                <div class="card-body">
                                    <div class="row justify-content-between align-items-center">
                                        <div class="col-4 text-center">
                                            <div>最短CT</div>
                                            <strong><span id="minCT">0</span>s</strong>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div>最长CT</div>
                                            <strong><span id="maxCT">0</span>s</strong>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div>平均CT</div>
                                            <strong><span id="avgCT">0</span>s</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表行 -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Slave分布</div>
                                <div class="card-body">
                                    <div id="slaveChart" class="chart-container"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Slot分布</div>
                                <div class="card-body">
                                    <div id="slotChart" class="chart-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试数据详情卡片 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center gap-2">
                                    <div>测试数据详情</div>
                                    <div class="ms-4 d-flex align-items-center gap-2">
                                        <select class="form-select form-select-sm" style="width: 130px;" id="snTypeSelect">
                                            <option value="Serial_Number">序列号</option>
                                            <option value="Customer_Serial_Number">客户序列号</option>
                                        </select>
                                        <input type="text" class="form-control form-control-sm" id="snDetailInput" 
                                               placeholder="请输入序列号" style="width: 200px;">
                                        <button class="btn btn-primary btn-sm" id="snSearchBtn">
                                            查询
                                        </button>
                                    </div>
                                </div>
                                <small>每5分钟自动刷新</small>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="dataTable">
                                    <thead>
                                        <tr>
                                            <th class="sortable" data-field="StartTestTime">开始时间<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="Serial_Number">序列号<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="Customer_Serial_Number">客户序列号<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="Slave">Slave<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="Slot">Slot<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="S_Slot">S-Slot<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="TestResult">测试结果<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="Station">站位<span class="sort-icon"></span></th>
                                            <th class="sortable" data-field="CycleTime">CT(s)<span class="sort-icon"></span></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tableBody"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div id="pagination" class="d-flex justify-content-center mt-3">
                        <button class="btn btn-outline-primary btn-sm mx-1" onclick="goToFirstPage()">首页</button>
                        <button class="btn btn-outline-primary btn-sm mx-1" onclick="goToPreviousPage()">上一页</button>
                        <span class="mx-2 align-self-center">当前第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页</span>
                        <button class="btn btn-outline-primary btn-sm mx-1" onclick="goToNextPage()">下一页</button>
                        <button class="btn btn-outline-primary btn-sm mx-1" onclick="goToLastPage()">末页</button>
                    </div>
                </div>

                <!-- 日志详情模态框 -->
                <div class="modal fade" id="logModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">日志详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="log-info mb-3">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>序列号：</strong><span id="logSN"></span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>测试时间：</strong><span id="logTime"></span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>站位：</strong><span id="logStation"></span>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <strong>日志路径：</strong>
                                        <span id="logPath" class="log-path text-muted small"></span>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered log-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 5%">序号</th>
                                                <th style="width: 25%" class="log-sortable" data-field="test_item">测试项<span class="sort-icon"></span></th>
                                                <th style="width: 20%" class="log-sortable" data-field="test_value">测试值<span class="sort-icon"></span></th>
                                                <th style="width: 10%" class="log-sortable" data-field="result">结果<span class="sort-icon"></span></th>
                                                <th style="width: 15%" class="log-sortable" data-field="lower_limit">下限<span class="sort-icon"></span></th>
                                                <th style="width: 15%" class="log-sortable" data-field="upper_limit">上限<span class="sort-icon"></span></th>
                                                <th style="width: 10%" class="log-sortable" data-field="error_code">错误码<span class="sort-icon"></span></th>
                                            </tr>
                                        </thead>
                                        <tbody id="logTableBody"></tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试项趋势图模态框 -->
                <div class="modal fade" id="itemDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">测试项趋势分析</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div id="progressContainer" class="mb-3" style="display: none;">
                                    <div class="progress">
                                        <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                            0%
                                        </div>
                                    </div>
                                    <div class="text-center mt-1">
                                        <small id="progressText">正在处理: 0 / 0 个文件</small>
                                    </div>
                                </div>
                                <div class="item-info mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>测试项：</strong><span id="itemName"></span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>规格范围：</strong>
                                            <span id="itemSpec"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="trend-filters mb-3">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">Slave筛选</label>
                                            <select class="form-select" id="trendSlaveFilter">
                                                <option value="">全部</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Slot筛选</label>
                                            <select class="form-select" id="trendSlotFilter">
                                                <option value="">全部</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div id="itemTrendChart" style="height: 400px;"></div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>最大值：</strong><span id="maxValue">-</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>最小值：</strong><span id="minValue">-</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>平均值：</strong><span id="avgValue">-</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>标准差：</strong><span id="stdValue">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading遮罩 -->
                <div class="loading d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="leap-panel" role="tabpanel">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-2">
                                <label>测试站位</label>
                                <select id="leapTestStationSelect" class="form-select">
                                    <option value="SOC" selected>SOC测试</option>
                                    <option value="VIU">VIU测试</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>选择EOL设备</label>
                                <select id="leapEolSelect" class="form-select">
                                    <option value="">全部设备</option>
                                    <option value="EOL1">EOL1</option>
                                    <option value="EOL2">EOL2</option>
                                    <option value="EOL3">EOL3</option>
                                    <option value="EOL4">EOL4</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>时间范围</label>
                                <select id="leapTimeRange" class="form-select">
                                    <option value="0" selected>今天</option>
                                    <option value="1">昨天</option>
                                    <option value="7">最近7天</option>
                                    <option value="30">最近30天</option>
                                    <option value="custom">自定义时间范围</option>
                                </select>
                            </div>
                            <div class="col-md-4" id="leapCustomDateInputs" style="display:none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label>开始时间</label>
                                        <input type="datetime-local" class="form-control" id="leapCustomDateStart">
                                    </div>
                                    <div class="col-md-6">
                                        <label>结束时间</label>
                                        <input type="datetime-local" class="form-control" id="leapCustomDateEnd">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label>测试结果</label>
                                <select id="leapResultFilter" class="form-select">
                                    <option value="">全部</option>
                                    <option value="PASSED">Pass</option>
                                    <option value="FAILED">Fail</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="loadLeapDb()">查询</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 零跑项目卡片区，移动到测试数据详情卡片上方 -->
                <div class="row mt-4" id="leap-summary-row">
                    <!-- 测试总览卡片 -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">测试总览</div>
                            <div class="card-body">
                                <!-- SOC测试统计 -->
                                <div class="mb-3">
                                    <h5 class="text-primary mb-2">SOC测试</h5>
                                    <div class="row">
                                        <div class="col-2 text-center">
                                            <small>总数(SN)</small><br>
                                            <strong><span id="soc-totalTests">0</span></strong>
                                        </div>
                                        <div class="col-2 text-center">
                                            <small class="text-success">通过(SN)</small><br>
                                            <strong class="text-success"><span id="soc-passCount">0</span></strong>
                                        </div>
                                        <div class="col-2 text-center">
                                            <small class="text-danger">失败(SN)</small><br>
                                            <strong class="text-danger"><span id="soc-failCount">0</span></strong>
                                        </div>
                                        <div class="col-3 text-center">
                                            <small class="text-primary">FPY</small><br>
                                            <strong class="text-primary"><span id="soc-passRate">0</span>%</strong>
                                        </div>
                                        <div class="col-3 text-center">
                                            <small class="text-info">实际产出</small><br>
                                            <strong class="text-info"><span id="soc-actualOutput">0</span></strong>
                                        </div>
                                    </div>
                                </div>
                                <!-- VIU测试统计 -->
                                <div>
                                    <h5 class="text-info mb-2">VIU测试</h5>
                                    <div class="row">
                                        <div class="col-2 text-center">
                                            <small>总数(SN)</small><br>
                                            <strong><span id="viu-totalTests">0</span></strong>
                                        </div>
                                        <div class="col-2 text-center">
                                            <small class="text-success">通过(SN)</small><br>
                                            <strong class="text-success"><span id="viu-passCount">0</span></strong>
                                        </div>
                                        <div class="col-2 text-center">
                                            <small class="text-danger">失败(SN)</small><br>
                                            <strong class="text-danger"><span id="viu-failCount">0</span></strong>
                                        </div>
                                        <div class="col-3 text-center">
                                            <small class="text-primary">FPY</small><br>
                                            <strong class="text-primary"><span id="viu-passRate">0</span>%</strong>
                                        </div>
                                        <div class="col-3 text-center">
                                            <small class="text-info">实际产出</small><br>
                                            <strong class="text-info"><span id="viu-actualOutput">0</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Station分布卡片 -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">Station分布</div>
                            <div class="card-body">
                                <div id="leap-station-chart" style="height: 260px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center gap-2">
                                <div>测试数据详情</div>
                                <div class="ms-4 d-flex align-items-center gap-2">
                                    <select class="form-select form-select-sm" style="width: 130px;" id="leapSnTypeSelect">
                                        <option value="Serial_Number">序列号</option>
                                        <option value="Customer_Serial_Number">客户序列号</option>
                                    </select>
                                    <input type="text" class="form-control form-control-sm" id="leapSnInput" 
                                           placeholder="请输入序列号" style="width: 200px;">
                                    <button class="btn btn-primary btn-sm" id="leapSnSearchBtn">
                                        查询
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="leap-dataTable">
                                <thead>
                                    <tr>
                                        <th class="sortable" data-field="TestTime">TestTime<span class="sort-icon"></span></th>
                                        <th class="sortable" data-field="Serial_Number">Serial_Number<span class="sort-icon"></span></th>
                                        <th class="sortable" data-field="TestResult">TestResult<span class="sort-icon"></span></th>
                                        <th class="sortable" data-field="Station">Station<span class="sort-icon"></span></th>
                                        <th class="sortable" data-field="Failure_item" style="min-width:180px;max-width:300px;">Failure_Item<span class="sort-icon"></span></th>
                                    </tr>
                                </thead>
                                <tbody id="leap-tableBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Top Issue 分布</span>
                        <div class="d-flex gap-2">
                            <select id="topIssueLimit" class="form-select form-select-sm" style="width: auto;">
                                <option value="10">Top 10</option>
                                <option value="20">Top 20</option>
                                <option value="50">Top 50</option>
                                <option value="100">Top 100</option>
                            </select>
                            <button id="aiAnalysisBtn" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-brain"></i> AI分析
                            </button>
                            <button id="refreshTopIssues" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div id="topIssueChart" style="height: 400px;"></div>
                            </div>
                            <div class="col-md-4">
                                <div id="topIssueTable" class="table-responsive" style="height: 400px; overflow-y: auto;">
                                    <!-- 这里动态填充Top Issue表格 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI分析结果卡片 -->
                <div class="card mb-4" id="aiAnalysisCard" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-brain text-success"></i> AI智能分析报告</span>
                        <div class="d-flex gap-2">
                            <span id="aiAnalysisTime" class="text-muted small"></span>
                            <button id="closeAiAnalysis" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-times"></i> 关闭
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="aiAnalysisContent">
                            <div class="text-center p-4">
                                <div class="spinner-border text-success" role="status">
                                    <span class="visually-hidden">AI分析中...</span>
                                </div>
                                <p class="mt-2 text-muted">AI正在分析数据，请稍候...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI配置模态框 -->
                <div class="modal fade" id="aiConfigModal" tabindex="-1" aria-labelledby="aiConfigModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="aiConfigModalLabel">
                                    <i class="fas fa-brain text-success"></i> AI分析配置
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="aiConfigContent">
                                    <div class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2 text-muted">正在加载配置...</p>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" id="saveAiConfig">保存配置</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 故障详情模态框 -->
                <div class="modal fade" id="failureDetailModal" tabindex="-1" aria-labelledby="failureDetailModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="failureDetailModalLabel">故障详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="failureDetailTableContainer">
                                    <!-- 这里将动态填充故障详情表格 -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 失败项详情模态框 -->
                <div class="modal fade" id="topIssueDetailModal" tabindex="-1" aria-labelledby="topIssueDetailModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="topIssueDetailModalLabel">失败项详细分布</h5>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="cleanupModalBackdrop()" title="清理界面">
                                        <i class="fas fa-broom"></i>
                                    </button>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                            </div>
                            <div class="modal-body">
                                <!-- 第一行：分布图表 -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">测试设备分布</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="stationDistributionChart" style="height: 300px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">时间分布趋势</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="timeDistributionChart" style="height: 300px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二行：详细记录 -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">详细记录</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="failureDetailTable" style="max-height: 400px; overflow-y: auto;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第三行：散点图 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-3">失败项测试值分布散点图</h6>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <label class="form-label small text-muted">选择要显示的测试项：</label>
                                                        <div id="subItemCheckboxes" class="d-flex flex-wrap gap-2" style="max-height: 120px; overflow-y: auto;">
                                                            <!-- 单选框将在这里动态生成 -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div id="scatterChart" style="height: 400px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 完整测试log模态框 -->
                <div class="modal fade" id="fullLogModal" tabindex="-1" aria-labelledby="fullLogModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="fullLogModalLabel">完整测试记录</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="fullLogModalContent">
                                    <!-- 这里将动态填充完整测试log -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 依赖 -->
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/moment.min.js"></script>

    <!-- Password Modal for Access Log -->
    <div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="passwordModalLabel">请输入密码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="passwordForm">
                        <div class="mb-3">
                            <label for="passwordInput" class="form-label">密码</label>
                            <input type="password" class="form-control" id="passwordInput" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">进入</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Access Log Viewer Modal -->
    <div class="modal fade" id="accessLogModal" tabindex="-1" aria-labelledby="accessLogModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="accessLogModalLabel">访问日志 (最新记录在前)</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-3">
                    <!-- 筛选控制区 -->
                    <div class="log-filter-section">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="ipFilter" class="form-label">IP地址筛选</label>
                            <input type="text" class="form-control" id="ipFilter" placeholder="输入IP地址">
                        </div>
                        <div class="col-md-3">
                            <label for="methodFilter" class="form-label">请求方法</label>
                            <select class="form-select" id="methodFilter">
                                <option value="">全部</option>
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="pathFilter" class="form-label">路径筛选</label>
                            <input type="text" class="form-control" id="pathFilter" placeholder="输入路径关键词">
                        </div>
                        <div class="col-md-3">
                            <label for="dateFilter" class="form-label">日期筛选</label>
                            <input type="date" class="form-control" id="dateFilter">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary" id="applyFilters">应用筛选</button>
                            <button type="button" class="btn btn-secondary" id="clearFilters">清除筛选</button>
                            <span class="ms-3 text-muted" id="recordCount">总记录数: 0</span>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-success" id="exportLogs">导出日志</button>
                        </div>
                    </div>
                    </div>

                    <!-- 日志表格 -->
                    <div class="table-responsive" style="max-height: 70vh; overflow-y: auto;">
                        <table class="table table-striped table-hover" id="accessLogTable">
                            <thead class="table-dark sticky-top">
                                <tr>
                                    <th style="cursor: pointer;" data-sort="timestamp">
                                        时间 <i class="fas fa-sort"></i>
                                    </th>
                                    <th style="cursor: pointer;" data-sort="ip">
                                        IP地址 <i class="fas fa-sort"></i>
                                    </th>
                                    <th style="cursor: pointer;" data-sort="method">
                                        方法 <i class="fas fa-sort"></i>
                                    </th>
                                    <th style="cursor: pointer;" data-sort="path">
                                        路径 <i class="fas fa-sort"></i>
                                    </th>
                                    <th style="cursor: pointer;" data-sort="user_agent">
                                        用户代理 <i class="fas fa-sort"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="accessLogTableBody">
                                <!-- 动态生成的日志记录 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控制 -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="d-flex align-items-center gap-2">
                            <select class="form-select form-select-sm" id="pageSize" style="width: auto; display: inline-block;">
                                <option value="50">50条/页</option>
                                <option value="100">100条/页</option>
                                <option value="200">200条/页</option>
                                <option value="500" selected>500条/页</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" id="refreshAccessLog" title="刷新访问日志">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <nav aria-label="日志分页">
                            <ul class="pagination pagination-sm mb-0" id="logPagination">
                                <!-- 动态生成分页 -->
                            </ul>
                        </nav>
                    </div>
                </div>
                <div class="modal-footer">
                     <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/daterangepicker.min.js"></script>
    <script src="/static/js/echarts.min.js"></script>

    <!-- 烟花效果相关脚本 -->
    <script src='https://s3-us-west-2.amazonaws.com/s.cdpn.io/329180/fscreen%401.0.1.js'></script>
    <script src='https://s3-us-west-2.amazonaws.com/s.cdpn.io/329180/Stage%400.1.4.js'></script>
    <script src='https://s3-us-west-2.amazonaws.com/s.cdpn.io/329180/MyMath.js'></script>
    
    <script>
        // 全局变量
        const PAGE_SIZE = 10;
        let currentData = [];
        let originalData = [];
        let logModal = null;
        let itemDetailModal = null;
        let refreshInterval = null;
        let passwordModal = null;
        let accessLogModal = null;
        let currentSort = {
            field: null,
            direction: 'asc'
        };
        let currentPage = 1;
        let totalPages = 1;
        let slaveChart = null;
        let slotChart = null;
        let itemTrendChart = null;
        let topIssueChart = null;
        let trendData = [];
        let filteredTrendData = [];
        let topIssueData = [];

        // 添加日志详情表格排序相关变量
        let currentLogSort = {
            field: null,
            direction: 'asc'
        };
        let currentLogData = [];

        // DOM加载完成后执行
        $(document).ready(function() {
            if (typeof $ === 'undefined') {
                console.error('jQuery not loaded!');
                return;
            }

            passwordModal = new bootstrap.Modal(document.getElementById('passwordModal'));
            accessLogModal = new bootstrap.Modal(document.getElementById('accessLogModal'));
            logModal = new bootstrap.Modal(document.getElementById('logModal'));
            itemDetailModal = new bootstrap.Modal(document.getElementById('itemDetailModal'));
            setupEventListeners();
            
            slaveChart = echarts.init(document.getElementById('slaveChart'));
            slotChart = echarts.init(document.getElementById('slotChart'));
            itemTrendChart = echarts.init(document.getElementById('itemTrendChart'));
            topIssueChart = echarts.init(document.getElementById('topIssueChart'));
            
            if ($('#stationSelect').val() === 'AP5') {
                $('#slaveSelectContainer').show();
                loadSlaves();
            }
            
            queryData();
            setupAutoRefresh();

            window.addEventListener('resize', function() {
                if (slaveChart) slaveChart.resize();
                if (slotChart) slotChart.resize();
                if (itemTrendChart) itemTrendChart.resize();
                if (topIssueChart) topIssueChart.resize();
                if (stationDistributionChart) stationDistributionChart.resize();
                if (timeDistributionChart) timeDistributionChart.resize();
                if (scatterChart) scatterChart.resize();
            });

            // 监听模态框显示事件
            $('#itemDetailModal').on('shown.bs.modal', function () {
                if (itemTrendChart) {
                    itemTrendChart.resize();
                }
            });

            // 监听失败项详情模态框显示事件，调整图表大小
            $('#topIssueDetailModal').on('shown.bs.modal', function () {
                setTimeout(() => {
                    if (stationDistributionChart) stationDistributionChart.resize();
                    if (timeDistributionChart) timeDistributionChart.resize();
                    if (scatterChart) scatterChart.resize();
                }, 100);
            });
        });

        // 设置事件监听器
        function setupEventListeners() {
            $('#timeRange').change(function() {
                const isCustom = $(this).val() === 'custom';
                $('#customDateInputs').toggle(isCustom);
            });

            $('#resultFilter').change(function() {
                filterAndDisplayData();
            });

            $('#stationSelect').change(function() {
                const isAP5 = $(this).val() === 'AP5';
                $('#slaveSelectContainer').toggle(isAP5);
                if (isAP5) {
                    loadSlaves();
                }
            });

            $('#queryBtn').click(function() {
                currentPage = 1;
                // 清除序列号输入框的内容
                $('#snDetailInput').val('');
                queryData();
            });

            // 修改排序事件绑定
            $('#dataTable').on('click', '.sortable', function() {
                const field = $(this).data('field');
                if (currentSort.field === field) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.field = field;
                    currentSort.direction = 'asc';
                }
                
                // 更新所有排序图标
                $('.sort-icon').removeClass('sort-asc sort-desc');
                $(this).find('.sort-icon').addClass(currentSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
                
                sortAndDisplayData();
            });

            $(document).off('click', '.show-log').on('click', '.show-log', function(e) {
                e.preventDefault();
                const rowData = {
                    serial_number: $(this).data('sn'),
                    start_time: $(this).data('time'),
                    station: $(this).data('station')
                };
                showLogDetails(rowData);
            });

            // 趋势图筛选器事件
            $('#trendSlaveFilter, #trendSlotFilter').change(function() {
                filterTrendData();
            });

            // 添加日志表格排序事件监听
            $('#logModal').on('click', '.log-sortable', function() {
                const field = $(this).data('field');
                if (currentLogSort.field === field) {
                    currentLogSort.direction = currentLogSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentLogSort.field = field;
                    currentLogSort.direction = 'asc';
                }
                
                // 更新所有排序图标
                $('#logModal .sort-icon').removeClass('sort-asc sort-desc');
                $(this).find('.sort-icon').addClass(currentLogSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
                
                sortLogData();
            });

            // 添加序列号类型选择事件
            $('#snTypeSelect').change(function() {
                const type = $(this).val();
                $('#snDetailInput').attr('placeholder', 
                    type === 'Serial_Number' ? '请输入序列号' : '请输入客户序列号');
            });

            // 添加序列号查询按钮事件
            $('#snSearchBtn').click(function() {
                const snType = $('#snTypeSelect').val();
                const snValue = $('#snDetailInput').val().trim();
                
                if (!snValue) {
                    alert('请输入序列号');
                    return;
                }
                
                // 显示加载动画
                showLoading();
                
                // 使用新的API端点直接从数据库查询
                fetch(`/api/search_by_sn?sn_type=${snType}&sn_value=${encodeURIComponent(snValue)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            throw new Error(data.error);
                        }

                        // 更新数据和显示
                        currentData = data;
                        originalData = [...data.recent_data];
                        
                        // 更新页面显示
                        currentPage = 1;
                        updateDisplay(currentData);
                        updatePagination();
                    })
                    .catch(error => {
                        console.error('序列号查询失败:', error);
                        alert('序列号查询失败: ' + error.message);
                    })
                    .finally(() => {
                        hideLoading();
                    });
            });

            // 添加输入框回车事件
            $('#snDetailInput').keypress(function(e) {
                if (e.which === 13) {  // 回车键
                    $('#snSearchBtn').click();
                }
            });

            // Top Issue 相关事件监听器
            $('#refreshTopIssues').click(function() {
                loadTopIssues();
            });

            $('#aiAnalysisBtn').click(function() {
                performAIAnalysis();
            });

            $('#closeAiAnalysis').click(function() {
                $('#aiAnalysisCard').hide();
            });

            $('#topIssueLimit').change(function() {
                loadTopIssues();
            });

            // AI配置相关事件监听器已移除

            $('#saveAiConfig').click(function() {
                saveAIConfig();
            });

            // 秘密日志入口
            $('#hiddenLogTrigger').on('dblclick', function() {
                passwordModal.show();
            });

            $('#passwordForm').on('submit', function(e) {
                e.preventDefault();
                const password = $('#passwordInput').val();
                if (password === 'taomao128') {
                    passwordModal.hide();
                    $('#passwordInput').val(''); // 清空密码
                    showAccessLog();
                } else {
                    alert('密码错误!');
                    $('#passwordInput').val('');
                }
            });
        }

        // 设置日志相关事件监听器
        function setupLogEventListeners() {
            // 表头排序
            $('#accessLogTable th[data-sort]').off('click').on('click', function() {
                const field = $(this).data('sort');
                if (logSortField === field) {
                    logSortDirection = logSortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    logSortField = field;
                    logSortDirection = 'asc';
                }

                // 更新排序图标
                $('#accessLogTable th i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
                const icon = $(this).find('i');
                icon.removeClass('fa-sort').addClass(logSortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down');

                currentLogPage = 1;
                renderLogTable();
            });

            // 筛选功能
            $('#applyFilters').off('click').on('click', function() {
                applyLogFilters();
            });

            $('#clearFilters').off('click').on('click', function() {
                clearLogFilters();
            });

            // 分页
            $('#logPagination').off('click', 'a').on('click', 'a', function(e) {
                e.preventDefault();
                const page = parseInt($(this).data('page'));
                if (page && page !== currentLogPage) {
                    currentLogPage = page;
                    renderLogTable();
                }
            });

            // 每页显示数量
            $('#pageSize').off('change').on('change', function() {
                logPageSize = parseInt($(this).val());
                currentLogPage = 1;
                renderLogTable();
            });

            // 刷新访问日志
            $('#refreshAccessLog').off('click').on('click', function() {
                showAccessLog();
            });

            // 导出日志
            $('#exportLogs').off('click').on('click', function() {
                exportAccessLogs();
            });

            // 回车键筛选
            $('#ipFilter, #pathFilter').off('keypress').on('keypress', function(e) {
                if (e.which === 13) {
                    applyLogFilters();
                }
            });
        }

        // 应用筛选
        function applyLogFilters() {
            const ipFilter = $('#ipFilter').val().toLowerCase();
            const methodFilter = $('#methodFilter').val();
            const pathFilter = $('#pathFilter').val().toLowerCase();
            const dateFilter = $('#dateFilter').val();

            filteredLogData = accessLogData.filter(entry => {
                // IP筛选
                if (ipFilter && !entry.ip.toLowerCase().includes(ipFilter)) {
                    return false;
                }

                // 方法筛选
                if (methodFilter && entry.method !== methodFilter) {
                    return false;
                }

                // 路径筛选
                if (pathFilter && !entry.path.toLowerCase().includes(pathFilter)) {
                    return false;
                }

                // 日期筛选
                if (dateFilter) {
                    const entryDate = entry.timestamp.split(' ')[0]; // 获取日期部分
                    if (entryDate !== dateFilter) {
                        return false;
                    }
                }

                return true;
            });

            currentLogPage = 1;
            renderLogTable();
        }

        // 清除筛选
        function clearLogFilters() {
            $('#ipFilter').val('');
            $('#methodFilter').val('');
            $('#pathFilter').val('');
            $('#dateFilter').val('');

            filteredLogData = [...accessLogData];
            currentLogPage = 1;
            renderLogTable();
        }

        // 导出访问日志
        function exportAccessLogs() {
            if (filteredLogData.length === 0) {
                alert('没有数据可导出');
                return;
            }

            const headers = ['时间', 'IP地址', '请求方法', '路径', '用户代理'];
            const csvContent = [
                headers.join(','),
                ...filteredLogData.map(entry => [
                    `"${entry.timestamp}"`,
                    `"${entry.ip}"`,
                    `"${entry.method}"`,
                    `"${entry.path}"`,
                    `"${entry.user_agent}"`
                ].join(','))
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `访问日志_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 访问日志相关变量
        let accessLogData = [];
        let filteredLogData = [];
        let currentLogPage = 1;
        let logPageSize = 500;
        let logSortField = 'timestamp';
        let logSortDirection = 'desc';

        // 显示访问日志
        async function showAccessLog() {
            try {
                showLoading();
                const response = await fetch('/api/access_log');
                const data = await response.json();
                if (data.error) throw new Error(data.error);

                // 处理不同格式的日志数据
                if (Array.isArray(data)) {
                    if (data.length === 0) {
                        accessLogData = [];
                        $('#accessLogTableBody').html('<tr><td colspan="5" class="text-center text-muted">日志文件为空或不存在</td></tr>');
                    } else {
                        accessLogData = data;
                        filteredLogData = [...accessLogData];
                        renderLogTable();
                    }
                } else if (data.raw_content) {
                    // 处理原始内容（当正则解析失败时）
                    parseRawLogContent(data.raw_content);
                } else {
                    // 兼容旧格式
                    accessLogData = [];
                    $('#accessLogTableBody').html('<tr><td colspan="5" class="text-center text-muted">日志文件为空或不存在</td></tr>');
                }

                setupLogEventListeners();
                accessLogModal.show();
            } catch (error) {
                console.error('获取访问日志失败:', error);
                alert('获取访问日志失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 解析原始日志内容
        function parseRawLogContent(rawContent) {
            const lines = rawContent.split('\n');
            const logPattern = /\[(.+?)\]\s+IP:\s+(.+?)\s*\|\s*Method:\s+(.+?)\s*\|\s*Path:\s+(.+?)\s*\|\s*User-Agent:\s+(.+)/;

            accessLogData = [];
            lines.forEach((line, index) => {
                const match = line.match(logPattern);
                if (match) {
                    accessLogData.push({
                        timestamp: match[1],
                        ip: match[2],
                        method: match[3],
                        path: match[4],
                        user_agent: match[5]
                    });
                }
            });

            filteredLogData = [...accessLogData];
            renderLogTable();
        }

        // 渲染日志表格
        function renderLogTable() {
            // 排序数据
            sortLogData();

            // 分页
            const startIndex = (currentLogPage - 1) * logPageSize;
            const endIndex = startIndex + logPageSize;
            const pageData = filteredLogData.slice(startIndex, endIndex);

            // 生成表格行
            const tbody = $('#accessLogTableBody');
            tbody.empty();

            if (pageData.length === 0) {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">没有找到匹配的记录</td></tr>');
                return;
            }

            pageData.forEach(entry => {
                const row = $(`
                    <tr>
                        <td class="text-nowrap">${entry.timestamp}</td>
                        <td class="text-nowrap">${entry.ip}</td>
                        <td><span class="badge bg-${getMethodColor(entry.method)}">${entry.method}</span></td>
                        <td class="text-break" style="max-width: 300px;">${entry.path}</td>
                        <td class="text-break" style="max-width: 400px;" title="${entry.user_agent}">${truncateText(entry.user_agent, 60)}</td>
                    </tr>
                `);
                tbody.append(row);
            });

            // 更新记录计数和分页
            updateLogRecordCount();
            renderLogPagination();
        }

        // 获取HTTP方法对应的颜色
        function getMethodColor(method) {
            const colors = {
                'GET': 'primary',
                'POST': 'success',
                'PUT': 'warning',
                'DELETE': 'danger',
                'PATCH': 'info'
            };
            return colors[method] || 'secondary';
        }

        // 截断文本
        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // 排序日志数据
        function sortLogData() {
            filteredLogData.sort((a, b) => {
                let aVal = a[logSortField];
                let bVal = b[logSortField];

                // 时间字段特殊处理
                if (logSortField === 'timestamp') {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                if (aVal < bVal) return logSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return logSortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }

        // 更新记录计数
        function updateLogRecordCount() {
            $('#recordCount').text(`总记录数: ${filteredLogData.length} / ${accessLogData.length}`);
        }

        // 渲染分页
        function renderLogPagination() {
            const totalPages = Math.ceil(filteredLogData.length / logPageSize);
            const pagination = $('#logPagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页
            pagination.append(`
                <li class="page-item ${currentLogPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentLogPage - 1}">上一页</a>
                </li>
            `);

            // 页码
            const startPage = Math.max(1, currentLogPage - 2);
            const endPage = Math.min(totalPages, currentLogPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === currentLogPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            }

            // 下一页
            pagination.append(`
                <li class="page-item ${currentLogPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentLogPage + 1}">下一页</a>
                </li>
            `);
        }

        // 更新趋势图筛选器
        function updateTrendFilters() {
            const slaveSelect = $('#trendSlaveFilter');
            const slotSelect = $('#trendSlotFilter');
            
            // 获取唯一的Slave和Slot值
            const slaves = [...new Set(trendData.map(item => item.slave))].sort();
            const slots = [...new Set(trendData.map(item => item.slot))].sort();
            
            // 更新Slave选项
            slaveSelect.empty().append('<option value="">全部</option>');
            slaves.forEach(slave => {
                if (slave) {
                    slaveSelect.append(`<option value="${slave}">Slave ${slave}</option>`);
                }
            });
            
            // 更新Slot选项
            slotSelect.empty().append('<option value="">全部</option>');
            slots.forEach(slot => {
                if (slot) {
                    slotSelect.append(`<option value="${slot}">Slot ${slot}</option>`);
                }
            });
        }

        // 过滤趋势数据
        function filterTrendData() {
            const selectedSlave = $('#trendSlaveFilter').val();
            const selectedSlot = $('#trendSlotFilter').val();
            
            filteredTrendData = trendData.filter(item => {
                const matchSlave = !selectedSlave || item.slave === selectedSlave;
                const matchSlot = !selectedSlot || item.slot === selectedSlot;
                return matchSlave && matchSlot;
            });

            updateTrendChart();
            updateTrendStats();
        }

        // 更新趋势统计数据
        function updateTrendStats() {
            if (!filteredTrendData || filteredTrendData.length === 0) {
                $('#maxValue').text('-');
                $('#minValue').text('-');
                $('#avgValue').text('-');
                $('#stdValue').text('-');
                return;
            }

            try {
                // 只过滤无效的数值，不因为ErrorCode为空而跳过
                const values = filteredTrendData
                    .map(item => parseFloat(item.value))
                    .filter(value => !isNaN(value));
                
                if (values.length === 0) {
                    console.warn('No valid values found for statistics');
                    return;
                }

                const max = Math.max(...values);
                const min = Math.min(...values);
                const avg = values.reduce((a, b) => a + b) / values.length;
                const variance = values.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / values.length;
                const std = Math.sqrt(variance);

                $('#maxValue').text(max.toFixed(3));
                $('#minValue').text(min.toFixed(3));
                $('#avgValue').text(avg.toFixed(3));
                $('#stdValue').text(std.toFixed(3));

                // 添加调试信息
                console.log('Stats calculation complete:', {
                    count: values.length,
                    max,
                    min,
                    avg,
                    std
                });
            } catch (error) {
                console.error('Error updating trend stats:', error);
            }
        }

        // 更新趋势图
        function updateTrendChart() {
            // 确保 DOM 元素存在
            const chartContainer = document.getElementById('itemTrendChart');
            if (!chartContainer) return;

            // 如果图表实例存在，先销毁它
            if (itemTrendChart) {
                itemTrendChart.dispose();
            }

            // 重新初始化图表
            itemTrendChart = echarts.init(chartContainer);

            if (!filteredTrendData || filteredTrendData.length === 0) {
                console.log('No data available for chart');
                return;
            }

            const itemName = $('#itemName').text();
            const specs = $('#itemSpec').text().split('~');
            const lowerLimit = parseFloat(specs[0].trim());
            const upperLimit = parseFloat(specs[1].trim());

            // 计算所有测试值的范围，确保将字符串转换为数字并处理可能的无效值
            const values = filteredTrendData.map(item => {
                const val = parseFloat(item.value);
                // 添加调试日志
                if (isNaN(val)) {
                    console.warn('Invalid value:', item);
                }
                return val;
            }).filter(val => !isNaN(val)); // 过滤掉无效值

            const yMin = Math.min(...values);
            const yMax = Math.max(...values);
            const yRange = yMax - yMin;
            const yPadding = Math.max(Math.abs(yRange * 0.1), 1);

            const yAxisMin = Math.floor(yMin - yPadding);
            const yAxisMax = Math.ceil(yMax + yPadding);

            const option = {
                title: {
                    text: `${itemName} 测试值分布`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const dataPoint = filteredTrendData[params.dataIndex];
                        let tooltipContent = `序列号: ${dataPoint.sn || '-'}<br/>` +
                                        `测试值: ${dataPoint.value || '-'}<br/>` +
                                        `时间: ${dataPoint.time || '-'}<br/>` +
                                        `Slave: ${dataPoint.slave || 'Slave未知'}<br/>` +
                                        `Slot: ${dataPoint.slot || 'Slot未知'}`;
                        
                        // 仅在ErrorCode存在且不为空时添加
                        if (dataPoint.error_code != null && dataPoint.error_code !== '') {
                            tooltipContent += `<br/>错误码: ${dataPoint.error_code}`;
                        }
                        
                        return tooltipContent;
                    }
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: filteredTrendData.map((_, index) => index + 1),
                    name: '测试序号',
                    nameLocation: 'middle',
                    nameGap: 30,
                    splitLine: {
                        show: true,
                        lineStyle: { type: 'dashed' }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '测试值',
                    nameLocation: 'middle',
                    nameGap: 50,
                    min: yAxisMin,
                    max: yAxisMax,
                    splitLine: {
                        show: true,
                        lineStyle: { type: 'dashed' }
                    },
                    axisLine: { show: true },
                    scale: true
                },
                series: [
                    {
                        name: itemName,
                        type: 'scatter',
                        symbolSize: 8,
                        data: filteredTrendData.map((item, index) => {
                            const value = parseFloat(item.value);
                            // 只过滤无效的数值，不因为ErrorCode为空而跳过
                            if (isNaN(value)) {
                                console.warn('Invalid value found:', item);
                                return [index + 1, null];
                            }
                            return [index + 1, value];
                        }).filter(item => item[1] !== null), // 只过滤掉无效的数值点
                        itemStyle: {
                            color: function(params) {
                                const value = parseFloat(filteredTrendData[params.dataIndex].value);
                                return (value >= lowerLimit && value <= upperLimit) ? '#67c23a' : '#f56c6c';
                            }
                        }
                    }
                ],
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100,
                        zoomOnMouseWheel: 'shift'
                    },
                    {
                        type: 'slider',
                        show: true,
                        bottom: 5,
                        height: 20,
                        showDetail: true
                    },
                    {
                        type: 'slider',
                        show: true,
                        yAxisIndex: 0,
                        right: 5,
                        width: 20,
                        showDetail: true
                    }
                ]
            };

            try {
                itemTrendChart.setOption(option);
                console.log('Chart updated successfully');
            } catch (error) {
                console.error('Error updating chart:', error);
            }
        }

        // 显示趋势图
        async function showItemTrend(itemName, lowerLimit, upperLimit) {
            try {
                showLoading();
                
                // 重置图表
                if (itemTrendChart) {
                    itemTrendChart.dispose();
                    itemTrendChart = echarts.init(document.getElementById('itemTrendChart'));
                }
                
                // 重置数据和筛选器
                trendData = [];
                filteredTrendData = [];
                $('#trendSlaveFilter').val('');
                $('#trendSlotFilter').val('');
                
                // 显示模态框并重置内容
                $('#itemName').text(itemName);
                $('#itemSpec').text(`${lowerLimit} ~ ${upperLimit}`);
                $('#maxValue').text('-');
                $('#minValue').text('-');
                $('#avgValue').text('-');
                $('#stdValue').text('-');
                
                itemDetailModal.show();
                
                // 重置和显示进度条
                $('#progressContainer').show();
                $('#progressBar')
                    .css('width', '0%')
                    .attr('aria-valuenow', 0)
                    .text('0%');
                $('#progressText').text('准备处理文件...');
                
                // 获取查询参数
                const params = new URLSearchParams({
                    time_range: $('#timeRange').val(),
                    station: $('#stationSelect').val(),
                    slave: $('#slaveSelect').val() || '',
                    item_name: itemName,
                    custom_date: $('#customDate').val() || ''
                });
                
                // 创建和配置 EventSource
                const eventSource = new EventSource(`/api/item_trend?${params}`);
                
                // 处理连接打开
                eventSource.onopen = function(event) {
                    console.log('SSE连接已建立');
                };

                // 处理错误
                eventSource.onerror = function(event) {
                    console.error('SSE连接错误:', event);
                    eventSource.close();
                    hideLoading();
                    $('#progressContainer').hide();
                    alert('获取趋势数据失败，请重试');
                };

                // 处理消息
                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('收到数据类型:', data.type);

                        if (data.type === 'error') {
                            throw new Error(data.error);
                        }

                        // 处理进度更新
                        if (data.type === 'progress') {
                            const percentage = data.percentage || 0;
                            $('#progressBar')
                                .css('width', `${percentage}%`)
                                .attr('aria-valuenow', percentage)
                                .text(`${percentage}%`);
                            $('#progressText').text(`正在处理: ${data.processed} / ${data.total} 个文件`);
                        }

                        // 处理完成消息
                        if (data.type === 'complete' && data.values && data.values.length > 0) {
                            trendData = data.values;
                            filteredTrendData = [...trendData];
                            
                            // 更新筛选器
                            updateTrendFilters();
                            
                            // 更新图表和统计
                            updateTrendChart();
                            updateTrendStats();
                            
                            // 隐藏加载动画
                            hideLoading();
                            
                            // 延迟隐藏进度条
                            setTimeout(() => {
                                $('#progressContainer').fadeOut();
                            }, 1000);
                            
                            // 关闭EventSource
                            eventSource.close();
                        }
                    } catch (error) {
                        console.error('处理数据时出错:', error);
                        eventSource.close();
                        hideLoading();
                        $('#progressContainer').hide();
                        alert('处理趋势数据时出错，请重试');
                    }
                };

                // 模态框关闭时清理
                $('#itemDetailModal').off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    if (eventSource) {
                        eventSource.close();
                    }
                    hideLoading();
                    
                    // 清理图表实例
                    if (itemTrendChart) {
                        itemTrendChart.dispose();
                        itemTrendChart = null;
                    }
                });
                
            } catch (error) {
                console.error('趋势图显示失败:', error);
                hideLoading();
                $('#progressContainer').hide();
                alert('趋势图显示失败: ' + error.message);
            }
        }

        // 加载Slave列表
        async function loadSlaves() {
            try {
                const response = await fetch(`/api/slaves?station=${$('#stationSelect').val()}`);
                const slaves = await response.json();
                
                const slaveSelect = $('#slaveSelect');
                slaveSelect.empty();
                slaveSelect.append('<option value="">全部</option>');
                
                slaves.forEach(slave => {
                    slaveSelect.append(`<option value="${slave}">${slave}</option>`);
                });
            } catch (error) {
                console.error('加载Slave失败:', error);
                alert('加载Slave失败: ' + error.message);
            }
        }

        // 查询数据
        async function queryData() {
            showLoading();
            
            try {
                const params = new URLSearchParams({
                    time_range: $('#timeRange').val(),
                    station: $('#stationSelect').val(),
                    slave: $('#slaveSelect').val() || ''
                });

                if ($('#timeRange').val() === 'custom') {
                    const startTime = $('#customDateStart').val();
                    const endTime = $('#customDateEnd').val();
                    if (startTime && endTime) {
                        params.append('custom_date_start', startTime);
                        params.append('custom_date_end', endTime);
                    }
                }

                const response = await fetch(`/api/data?${params}`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                currentData = data;
                originalData = [...data.recent_data];
                updateDisplay(data);
                filterAndDisplayData();
                updatePagination();

                // 加载Top Issue数据
                loadTopIssues();
            } catch (error) {
                console.error('查询失败:', error);
                alert('查询失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 数据筛选和显示
        function filterAndDisplayData() {
            const resultFilter = $('#resultFilter').val();
            const snType = $('#snTypeSelect').val();
            const snValue = $('#snDetailInput').val().trim();
            
            currentData.recent_data = originalData.filter(item => {
                const matchResult = !resultFilter || item.TestResult === resultFilter;
                const matchSN = !snValue || 
                              (item[snType]?.toLowerCase() || '').includes(snValue.toLowerCase());
                return matchResult && matchSN;
            });
            
            currentPage = 1;
            sortAndDisplayData();
            updatePagination();
            updateTable(getPaginatedData());
        }

        // 更新显示
        function updateDisplay(data) {
            updateTestOverview(data.test_results);
            updateCTStats(data.ct_stats);
            updateCharts(data);
            updateTable(getPaginatedData());
        }

        // 更新测试总览
        function updateTestOverview(results) {
            const testsBySerial = new Map();
            
            const sortedData = [...originalData].sort((a, b) => 
                new Date(a.StartTestTime) - new Date(b.StartTestTime)
            );
            
            sortedData.forEach(item => {
                if (!testsBySerial.has(item.Serial_Number)) {
                    testsBySerial.set(item.Serial_Number, []);
                }
                testsBySerial.get(item.Serial_Number).push(item);
            });
            
            let firstTimePass = 0;
            const firstPassSNs = [];
            
            testsBySerial.forEach((tests, serialNumber) => {
                const firstTest = tests[0];
                if (firstTest && firstTest.TestResult === 'PASSED') {
                    firstTimePass++;
                    firstPassSNs.push(serialNumber);
                }
            });
            
            const totalUniqueSN = testsBySerial.size;
            
            $('#totalTests').text(results.total);
            $('#passCount').text(results.pass);
            $('#failCount').text(results.fail);
            const passRate = totalUniqueSN ? ((firstTimePass / totalUniqueSN) * 100).toFixed(2) : '0.00';
            $('#passRate').text(passRate);

            const tooltipContent = `
                <div class="text-start">
                    <p class="mb-1"><strong>FPY计算详情:</strong></p>
                    <p class="mb-1">- 总序列号数量: ${totalUniqueSN}</p>
                    <p class="mb-1">- 首次测试通过数量: ${firstTimePass}</p>
                    <p class="mb-1">- 计算方式: (首次测试通过数量 / 总序列号数量) × 100%</p>
                    <p class="mb-1">- 计算结果: (${firstTimePass} / ${totalUniqueSN}) × 100% = ${passRate}%</p>
                    <p class="mb-1">- 说明: 只有第一次测试就通过的产品才计入FPY</p>
                </div>
            `;
            
            const tooltip = bootstrap.Tooltip.getInstance('#fpyTooltip');
            if (tooltip) {
                tooltip.dispose();
            }
            
            new bootstrap.Tooltip('#fpyTooltip', {
                title: tooltipContent,
                html: true
            });
        }

        // 更新CT统计
        function updateCTStats(stats) {
            const resultFilter = $('#resultFilter').val();
            let filteredData = currentData.recent_data;
            
            if (resultFilter) {
                filteredData = currentData.recent_data.filter(item => item.TestResult === resultFilter);
            }

            if (filteredData.length === 0) {
                $('#minCT').text('0.00');
                $('#maxCT').text('0.00');
                $('#avgCT').text('0.00');
                return;
            }

            const cycleTimeData = filteredData.map(item => parseFloat(item.CycleTime));
            const minCT = Math.min(...cycleTimeData);
            const maxCT = Math.max(...cycleTimeData);
            const avgCT = cycleTimeData.reduce((a, b) => a + b, 0) / cycleTimeData.length;

            $('#minCT').text(minCT.toFixed(2));
            $('#maxCT').text(maxCT.toFixed(2));
            $('#avgCT').text(avgCT.toFixed(2));
        }

        // 更新图表
        function updateCharts(data) {
            // Slave分布图表
            const slaveOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                legend: {
                    data: ['通过', '失败', '失败率']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: data.slave_distribution.map(item => item.name),
                    axisLabel: { rotate: 45 }
                }],
                yAxis: [
                    {
                        type: 'value',
                        name: '数量'
                    },
                    {
                        type: 'value',
                        name: '失败率(%)',
                        max: 100
                    }
                ],
                series: [
                    {
                        name: '通过',
                        type: 'bar',
                        stack: 'total',
                        data: data.slave_distribution.map(item => item.passed)
                    },
                    {
                        name: '失败',
                        type: 'bar',
                        stack: 'total',
                        data: data.slave_distribution.map(item => item.failed)
                    },
                    {
                        name: '失败率',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.slave_distribution.map(item => item.failRate)
                    }
                ]
            };
            slaveChart.setOption(slaveOption);

            // Slot分布图表
            const slotOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                legend: {
                    data: ['通过', '失败', '失败率']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: data.slot_distribution.map(item => item.name),
                    axisLabel: { rotate: 45 }
                }],
                yAxis: [
                    {
                        type: 'value',
                        name: '数量'
                    },
                    {
                        type: 'value',
                        name: '失败率(%)',
                        max: 100
                    }
                ],
                series: [
                    {
                        name: '通过',
                        type: 'bar',
                        stack: 'total',
                        data: data.slot_distribution.map(item => item.passed)
                    },
                    {
                        name: '失败',
                        type: 'bar',
                        stack: 'total',
                        data: data.slot_distribution.map(item => item.failed)
                    },
                    {
                        name: '失败率',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.slot_distribution.map(item => item.failRate)
                    }
                ]
            };
            slotChart.setOption(slotOption);
        }

        // 排序和显示数据
        function sortAndDisplayData() {
            if (!currentData || !currentData.recent_data || !currentSort.field) return;
            
            const sortedData = [...currentData.recent_data].sort((a, b) => {
                let valueA = a[currentSort.field];
                let valueB = b[currentSort.field];
                
                // 处理特殊字段的排序
                if (currentSort.field === 'StartTestTime' || currentSort.field === 'EndTestTime') {
                    valueA = new Date(valueA).getTime();
                    valueB = new Date(valueB).getTime();
                } else if (currentSort.field === 'CycleTime' || currentSort.field === 'S_Slot') {
                    valueA = parseFloat(valueA) || 0;
                    valueB = parseFloat(valueB) || 0;
                } else if (currentSort.field === 'Slave' || currentSort.field === 'Slot') {
                    valueA = parseInt(valueA) || 0;
                    valueB = parseInt(valueB) || 0;
                }
                
                if (valueA < valueB) return currentSort.direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return currentSort.direction === 'asc' ? 1 : -1;
                return 0;
            });

            currentData.recent_data = sortedData;
            updateTable(getPaginatedData());
        }

        // 获取分页数据
        function getPaginatedData() {
            if (!currentData || !currentData.recent_data) return [];
            const start = (currentPage - 1) * PAGE_SIZE;
            const end = start + PAGE_SIZE;
            return currentData.recent_data.slice(start, end);
        }

        // 更新分页信息
        function updatePagination() {
            if (!currentData || !currentData.recent_data) {
                totalPages = 1;
                currentPage = 1;
            } else {
                totalPages = Math.ceil(currentData.recent_data.length / PAGE_SIZE);
                if (currentPage > totalPages) {
                    currentPage = totalPages;
                }
            }
            
            $('#currentPage').text(currentPage);
            $('#totalPages').text(totalPages);

            // 更新按钮状态
            $('button:contains("首页"), button:contains("上一页")').prop('disabled', currentPage === 1);
            $('button:contains("末页"), button:contains("下一页")').prop('disabled', currentPage === totalPages);
        }

        // 分页控制函数
        function goToFirstPage() {
            if (currentPage !== 1) {
                currentPage = 1;
                updateTable(getPaginatedData());
                updatePagination();
            }
        }

        function goToPreviousPage() {
            if (currentPage > 1) {
                currentPage--;
                updateTable(getPaginatedData());
                updatePagination();
            }
        }

        function goToNextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                updateTable(getPaginatedData());
                updatePagination();
            }
        }

        function goToLastPage() {
            if (currentPage !== totalPages) {
                currentPage = totalPages;
                updateTable(getPaginatedData());
                updatePagination();
            }
        }

        // 更新表格
        function updateTable(data) {
            const tableBody = $('#tableBody');
            tableBody.empty();

            if (!data || data.length === 0) {
                tableBody.append('<tr><td colspan="9" class="text-center">暂无数据</td></tr>');
                return;
            }

            data.forEach(row => {
                const tr = $('<tr>');
                const startTime = moment.utc(row.StartTestTime).subtract(0, 'hours').format('YYYY/MM/DD HH:mm:ss');
                tr.append(`<td>${startTime}</td>`);
                tr.append(`<td>${row.Serial_Number}</td>`);
                tr.append(`<td>${row.Customer_Serial_Number || ''}</td>`);
                tr.append(`<td>${row.Slave}</td>`);
                tr.append(`<td>${row.Slot}</td>`);
                tr.append(`<td>${row.S_Slot || ''}</td>`);
                
                const resultClass = row.TestResult === 'PASSED' ? 'text-success' : 'text-danger';
                const resultLink = $('<a>')
                    .addClass(`show-log cursor-pointer ${resultClass}`)
                    .text(row.TestResult)
                    .data('sn', row.Serial_Number)
                    .data('time', startTime)
                    .data('station', row.Station);
                tr.append($('<td>').append(resultLink));
                
                tr.append(`<td>${row.Station}</td>`);
                tr.append(`<td>${parseFloat(row.CycleTime).toFixed(2)}</td>`);
                tableBody.append(tr);
            });

            // 重新绑定查看日志事件
            $('.show-log').off('click').on('click', function(e) {
                e.preventDefault();
                const rowData = {
                    serial_number: $(this).data('sn'),
                    start_time: $(this).data('time'),
                    station: $(this).data('station')
                };
                showLogDetails(rowData);
            });
        }

        // 显示日志详情
        async function showLogDetails(data) {
            try {
                showLoading();

                $('#logSN').text(data.serial_number);
                $('#logTime').text(data.start_time);
                $('#logStation').text(data.station);

                const response = await fetch(`/api/read_log?serial_number=${data.serial_number}&start_time=${data.start_time}&station=${data.station}`);
                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                if (result.multiple_logs) {
                    const targetTime = moment(data.start_time, 'YYYY/MM/DD HH:mm:ss');
                    const targetTimeStr = targetTime.format('YYYYMMDD_HHmm');
                    
                    let matchedLog = null;
                    for (const logPath of result.multiple_logs) {
                        const filename = logPath.split('\\').pop();
                        if (filename.includes(targetTimeStr)) {
                            matchedLog = logPath;
                            break;
                        }
                    }

                    if (matchedLog) {
                        const detailResponse = await fetch(`/api/read_log?log_path=${matchedLog}`);
                        const detailResult = await detailResponse.json();
                        updateLogDisplay(detailResult);
                    } else {
                        throw new Error('未找到匹配的日志文件');
                    }
                } else {
                    updateLogDisplay(result);
                }

                logModal.show();
            } catch (error) {
                console.error('获取日志失败:', error);
                alert('获取日志失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 更新日志显示
        function updateLogDisplay(result) {
            currentLogData = result.parsed_results;  // 保存数据到全局变量
            displaySortedLogData(currentLogData);  // 使用新的显示函数
            $('#logPath').text(result.log_path);
        }

        // 显示排序后的日志数据
        function displaySortedLogData(data) {
            const logTableBody = $('#logTableBody');
            logTableBody.empty();

            data.forEach((item, index) => {
                const resultClass = (item.result.toUpperCase() === 'PASS') ? 'result-pass' : 'result-fail';
                const tr = `<tr>
                    <td>${index + 1}</td>
                    <td>
                        <span class="test-item-link" 
                              onclick="showItemTrend('${item.test_item}', ${item.lower_limit}, ${item.upper_limit})"
                        >${item.test_item}</span>
                    </td>
                    <td>${item.test_value}</td>
                    <td class="${resultClass}">${item.result}</td>
                    <td>${item.lower_limit}</td>
                    <td>${item.upper_limit}</td>
                    <td>${item.error_code}</td>
                </tr>`;
                logTableBody.append(tr);
            });
        }

        // 日志表格排序函数
        function sortLogData() {
            if (!currentLogData || !currentLogSort.field) return;

            const sortedData = [...currentLogData].sort((a, b) => {
                let valueA = a[currentLogSort.field];
                let valueB = b[currentLogSort.field];

                // 处理特殊字段的排序
                if (['test_value', 'lower_limit', 'upper_limit'].includes(currentLogSort.field)) {
                    // 数值类型排序
                    valueA = parseFloat(valueA) || 0;
                    valueB = parseFloat(valueB) || 0;
                } else if (currentLogSort.field === 'error_code') {
                    // 错误码排序，处理空值
                    valueA = valueA || '';
                    valueB = valueB || '';
                }

                if (valueA < valueB) return currentLogSort.direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return currentLogSort.direction === 'asc' ? 1 : -1;
                return 0;
            });

            displaySortedLogData(sortedData);
        }

        // 设置自动刷新
        function setupAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            refreshInterval = setInterval(queryData, 5 * 60 * 1000); // 5分钟刷新一次
        }

        // Loading控制
        function showLoading() {
            $('.loading').removeClass('d-none');
        }

        function hideLoading() {
            $('.loading').addClass('d-none');
        }

        // 当页面关闭或刷新时清理定时器
        window.onbeforeunload = function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        };

        // 初始化工具提示
        function initTooltips() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // 格式化日期时间
        function formatDateTime(date) {
            return moment(date).format('YYYY/MM/DD HH:mm:ss');
        }

        // 格式化数字
        function formatNumber(number, decimals = 2) {
            return Number(number).toFixed(decimals);
        }

        // 处理API错误
        function handleApiError(error) {
            console.error('API错误:', error);
            alert('操作失败: ' + (error.message || '未知错误'));
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 处理图表resize的防抖
        const debouncedResize = debounce(() => {
            if (slaveChart) slaveChart.resize();
            if (slotChart) slotChart.resize();
            if (itemTrendChart) itemTrendChart.resize();
        }, 250);

        // 监听窗口大小变化
        window.addEventListener('resize', debouncedResize);

        // 检查浏览器是否支持 EventSource
        function checkEventSourceSupport() {
            if (!window.EventSource) {
                console.error('此浏览器不支持 EventSource');
                alert('您的浏览器不支持实时数据更新功能，请使用现代浏览器访问本系统。');
                return false;
            }
            return true;
        }

        // 初始化页面时检查浏览器兼容性
        function checkBrowserCompatibility() {
            if (!checkEventSourceSupport()) {
                // 如果不支持 EventSource，禁用相关功能
                $('.test-item-link').css('pointer-events', 'none')
                                  .css('color', 'gray')
                                  .attr('title', '您的浏览器不支持此功能');
            }
        }

        // 在DOM加载完成后执行的初始化函数
        function initializeApp() {
            checkBrowserCompatibility();
            initTooltips();
            setupEventListeners();
            
            if ($('#stationSelect').val() === 'AP5') {
                $('#slaveSelectContainer').show();
                loadSlaves();
            }
            
            queryData();
            setupAutoRefresh();
        }

        // 在jQuery文档就绪后执行初始化
        $(document).ready(function() {
            // 监听模态框的显示和隐藏事件
            $('#itemDetailModal').on('shown.bs.modal', function () {
                if (itemTrendChart) {
                    itemTrendChart.resize();
                }
            });

            // 监听模态框关闭事件
            $('#itemDetailModal').on('hidden.bs.modal', function () {
                if (itemTrendChart) {
                    itemTrendChart.dispose();
                    itemTrendChart = null;
                }
                // 清空数据
                trendData = [];
                filteredTrendData = [];
            });
        });

        // 工具函数：将英文日期转为 YYYYMMDDHHmmss 和 YYYY/MM/DD HH:mm:ss
        function parseTestTime(str) {
            // 兼容 Sun, 18 May 2025 23:54:42 GMT
            const d = new Date(str);
            if (isNaN(d.getTime())) return { raw: str, compact: str, display: str };
            const pad = n => n.toString().padStart(2, '0');
            const compact = d.getFullYear() + pad(d.getMonth() + 1) + pad(d.getDate()) +
                pad(d.getHours()) + pad(d.getMinutes()) + pad(d.getSeconds());
            const display = `${d.getFullYear()}/${pad(d.getMonth() + 1)}/${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
            return { raw: str, compact, display };
        }

        // 零跑项目序列号查询
        function setupLeapSnSearch() {
            // 添加序列号类型选择事件
            $('#leapSnTypeSelect').change(function() {
                const type = $(this).val();
                $('#leapSnInput').attr('placeholder', 
                    type === 'Serial_Number' ? '请输入序列号' : '请输入客户序列号');
            });

            $('#leapSnSearchBtn').click(function() {
                const snType = $('#leapSnTypeSelect').val();
                const snValue = $('#leapSnInput').val().trim();
                
                if (!snValue) {
                    alert('请输入序列号');
                    return;
                }
                
                // 显示加载提示
                $('#leap-tableBody').html('<tr><td colspan="6">加载中...</td></tr>');
                
                fetch(`/api/leap8155/search_by_sn?sn_type=${snType}&sn_value=${encodeURIComponent(snValue)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            throw new Error(data.error);
                        }
                        
                        // 保存数据到全局变量
                        window.leapData = data;

                        // 重置分页并更新表格显示
                        currentLeapPage = 1;
                        totalLeapPages = Math.ceil(data.length / LEAP_PAGE_SIZE);
                        updateLeapTable(currentLeapPage);
                    })
                    .catch(error => {
                        console.error('序列号查询失败:', error);
                        $('#leap-tableBody').html(`<tr><td colspan="6" class="text-danger">查询失败: ${error.message}</td></tr>`);
                    });
            });

            // 添加回车键触发查询
            $('#leapSnInput').keypress(function(e) {
                if (e.which === 13) {  // 回车键
                    $('#leapSnSearchBtn').click();
                }
            });
        }

        // 在document ready中调用setup函数
        $(document).ready(function() {
            setupLeapSnSearch();
            // ... 其他初始化代码 ...
        });

        // 全局分页变量
        const LEAP_PAGE_SIZE = 10;
        let currentLeapPage = 1;
        let totalLeapPages = 1;

        // 全局排序状态变量
        let leapSortState = {
            field: null,
            direction: 'asc'
        };

        // 安全转义传入 onclick 的字符串，防止特殊字符导致 JS 语法错误
        function escapeJsString(str) {
            if (!str) return '';
            return str
                .replace(/\\/g, '\\\\')
                .replace(/'/g, "\\'")
                .replace(/"/g, '\\"')
                .replace(/\r/g, '\\r')
                .replace(/\n/g, '\\n');
        }

        // 全局 updateLeapTable 函数，供所有零跑项目表格渲染调用
        function updateLeapTable(page = 1) {
            const LEAP_PAGE_SIZE = 10;
            const data = window.leapData || [];
            const start = (page - 1) * LEAP_PAGE_SIZE;
            const end = start + LEAP_PAGE_SIZE;
            const pageData = data.slice(start, end);
            let html = '';
            if (pageData && pageData.length > 0) {
                pageData.forEach(row => {
                    // TestTime减8小时
                    let dateObj = new Date(row.TestTime);
                    dateObj.setHours(dateObj.getHours() - 8);
                    let displayTime = `${dateObj.getFullYear()}/${(dateObj.getMonth()+1).toString().padStart(2,'0')}/${dateObj.getDate().toString().padStart(2,'0')} ${dateObj.getHours().toString().padStart(2,'0')}:${dateObj.getMinutes().toString().padStart(2,'0')}:${dateObj.getSeconds().toString().padStart(2,'0')}`;
                    // Failure_Item只显示第一个分号前的内容的第二个字段
                    let failureRaw = row.Failure_item || row.failure_item;
                    let failureItem = '-';
                    let failureEscaped = escapeJsString(failureRaw || '');
                    if (failureRaw) {
                        let firstEntry = failureRaw.split(';')[0].trim();
                        let arr = firstEntry.split(',');
                        if (arr.length > 1) failureItem = arr[1];
                        else failureItem = firstEntry;
                    }
                    let resultClass = '';
                    if (row.TestResult === 'Passed' || row.TestResult === 'Pass' || row.TestResult === 'PASSED') resultClass = 'text-success';
                    else if (row.TestResult === 'Failed' || row.TestResult === 'Fail' || row.TestResult === 'FAILED') resultClass = 'text-danger';
                    else resultClass = 'text-warning'; // 其他结果归为Other
                    html += `<tr>
                        <td>${displayTime}</td>
                        <td>${row.Serial_Number}</td>
                        <td><a href="javascript:void(0)" class="${resultClass}" style="cursor:pointer;text-decoration:underline;" onclick="showLeapFullLog('${row.Serial_Number}','${displayTime}','${row.Station}')">${row.TestResult}</a></td>
                        <td>${row.Station || '-'}</td>
                        <td style="max-width:300px;word-break:break-all;">
                            ${failureItem === '-' ? '-' : `<a href=\"javascript:void(0)\" onclick=\"showFailureDetail('${failureEscaped}')\">${failureItem}</a>`}
                        </td>
                    </tr>`;
                });
            } else {
                html = '<tr><td colspan="5">无数据</td></tr>';
            }
            $('#leap-tableBody').html(html);
            // 更新分页
            totalLeapPages = Math.ceil(data.length / LEAP_PAGE_SIZE);
            updateLeapPagination(page, totalLeapPages);
        }

        // 专门用于加载测试总览统计的函数
        async function loadLeapDbForSummary() {
            const time_range = $('#leapTimeRange').val();
            const result = $('#leapResultFilter').val();

            // 注意：测试总览需要获取所有站位的数据，不受当前选择的测试站位影响
            let url = `/api/leap8155/data?time_range=${time_range}`;

            // 测试总览不应该受结果筛选影响，需要显示所有数据的统计
            // 如果需要按结果筛选，可以取消下面这行的注释
            // if (result) url += `&result=${result}`;

            if (time_range === 'custom') {
                const startTime = $('#leapCustomDateStart').val();
                const endTime = $('#leapCustomDateEnd').val();
                if (startTime && endTime) {
                    url += `&custom_date_start=${encodeURIComponent(startTime)}&custom_date_end=${encodeURIComponent(endTime)}`;
                }
            }

            try {
                const res = await fetch(url);
                const data = await res.json();

                // 只更新测试总览，不更新表格
                updateLeapSummaryAndChart(data);

                // 同时加载Top Issue数据
                loadTopIssues();

            } catch (error) {
                console.error('加载测试总览数据失败:', error);
            }
        }

        // 加载零跑数据库表
        async function loadLeapDb() {
            const testStation = $('#leapTestStationSelect').val();
            const eol = $('#leapEolSelect').val();
            const time_range = $('#leapTimeRange').val();
            const result = $('#leapResultFilter').val();

            let url = `/api/leap8155/data?time_range=${time_range}&result=${result}`;

            // 根据测试站位过滤EOL设备
            if (eol) {
                url += `&eol=${eol}`;
            } else {
                // 如果没有选择具体EOL设备，根据测试站位过滤
                if (testStation === 'SOC') {
                    url += `&test_station=SOC`; // 后端需要支持这个参数
                } else if (testStation === 'VIU') {
                    url += `&test_station=VIU`; // 后端需要支持这个参数
                }
            }

            if (time_range === 'custom') {
                const startTime = $('#leapCustomDateStart').val();
                const endTime = $('#leapCustomDateEnd').val();
                if (startTime && endTime) {
                    url += `&custom_date_start=${encodeURIComponent(startTime)}&custom_date_end=${encodeURIComponent(endTime)}`;
                }
            }

            $('#leap-tableBody').html('<tr><td colspan="6">加载中...</td></tr>');
            try {
                const res = await fetch(url);
                const data = await res.json();
                window.leapData = data; // 保存数据到全局变量，用于排序
                
                // 重置分页变量
                currentLeapPage = 1;
                totalLeapPages = Math.ceil(data.length / LEAP_PAGE_SIZE);

                // 初始化显示第一页
                updateLeapTable(currentLeapPage);

                // 同时加载完整数据用于测试总览统计
                loadLeapDbForSummary();
                
                // 添加分页控件
                if (!$('#leap-pagination').length) {
                    $('#leap-dataTable').after(`
                        <div id="leap-pagination" class="d-flex justify-content-center mt-3">
                            <button class="btn btn-outline-primary btn-sm mx-1" id="leap-first-page">首页</button>
                            <button class="btn btn-outline-primary btn-sm mx-1" id="leap-prev-page">上一页</button>
                            <span class="mx-2 align-self-center">当前第 <span id="leap-current-page">1</span> / <span id="leap-total-pages">1</span> 页</span>
                            <button class="btn btn-outline-primary btn-sm mx-1" id="leap-next-page">下一页</button>
                            <button class="btn btn-outline-primary btn-sm mx-1" id="leap-last-page">末页</button>
                        </div>
                    `);
                }
                
                // 绑定分页事件
                $('#leap-first-page').off('click').on('click', () => {
                    if (currentLeapPage !== 1) {
                        currentLeapPage = 1;
                        updateLeapTable(currentLeapPage);
                    }
                });

                $('#leap-prev-page').off('click').on('click', () => {
                    if (currentLeapPage > 1) {
                        currentLeapPage--;
                        updateLeapTable(currentLeapPage);
                    }
                });

                $('#leap-next-page').off('click').on('click', () => {
                    if (currentLeapPage < totalLeapPages) {
                        currentLeapPage++;
                        updateLeapTable(currentLeapPage);
                    }
                });

                $('#leap-last-page').off('click').on('click', () => {
                    if (currentLeapPage !== totalLeapPages) {
                        currentLeapPage = totalLeapPages;
                        updateLeapTable(currentLeapPage);
                    }
                });

                // 绑定排序事件 - 只绑定零跑项目表格的排序
                $('#leap-dataTable .sortable').off('click').on('click', function() {
                    const field = $(this).data('field');

                    // 更新排序状态
                    if (leapSortState.field === field) {
                        leapSortState.direction = leapSortState.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        leapSortState.field = field;
                        leapSortState.direction = 'asc';
                    }

                    // 更新排序图标
                    $('#leap-dataTable .sort-icon').removeClass('sort-asc sort-desc');
                    $(this).find('.sort-icon').addClass(leapSortState.direction === 'asc' ? 'sort-asc' : 'sort-desc');

                    // 排序数据
                    const sortedData = [...window.leapData].sort((a, b) => {
                        let valueA = a[field];
                        let valueB = b[field];

                        if (field === 'TestTime') {
                            valueA = new Date(valueA);
                            valueB = new Date(valueB);
                        }

                        if (valueA < valueB) return leapSortState.direction === 'asc' ? -1 : 1;
                        if (valueA > valueB) return leapSortState.direction === 'asc' ? 1 : -1;
                        return 0;
                    });

                    // 更新全局数据并重置到第一页
                    window.leapData = sortedData;
                    currentLeapPage = 1;
                    updateLeapTable(currentLeapPage);
                });
                
            } catch (error) {
                console.error('加载数据失败:', error);
                $('#leap-tableBody').html('<tr><td colspan="6" class="text-danger">加载数据失败</td></tr>');
            }
        }

        // 更新分页控件状态
        function updateLeapPagination(currentPage, totalPages) {
            $('#leap-current-page').text(currentPage);
            $('#leap-total-pages').text(totalPages);
            $('#leap-first-page, #leap-prev-page').prop('disabled', currentPage === 1);
            $('#leap-next-page, #leap-last-page').prop('disabled', currentPage === totalPages);
        }

        // 显示故障详情
        function showFailureDetail(failureData) {
            console.log('显示故障详情:', failureData); // 添加调试日志
            
            if (!failureData || failureData === '-') {
                $('#failureDetailTableContainer').html('<div class="text-muted">无详细内容</div>');
                new bootstrap.Modal(document.getElementById('failureDetailModal')).show();
                return;
            }

            // 解析内容
            const lines = failureData.split(';').map(s => s.trim()).filter(s => s);
            let html = `
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center">Test Seq</th>
                                <th>step_name</th>
                                <th>StandardValue</th>
                                <th>MeasuredValue</th>
                                <th>LowLimit</th>
                                <th>HighLimit</th>
                                <th>Result</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            lines.forEach((line, idx) => {
                const arr = line.split(',');
                const resultClass = (arr[6] && arr[6].trim().toLowerCase() === 'pass') ? 'table-success' : 'table-danger';
                html += `
                    <tr class="${resultClass}">
                        <td class="text-center">${idx + 1}</td>
                        <td>${arr[1] || ''}</td>
                        <td>${arr[2] || ''}</td>
                        <td>${arr[3] || ''}</td>
                        <td>${arr[4] || ''}</td>
                        <td>${arr[5] || ''}</td>
                        <td>${arr[6] || ''}</td>
                        <td>${arr[7] || ''}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            $('#failureDetailTableContainer').html(html);

            // 确保模态框存在
            const modal = document.getElementById('failureDetailModal');
            if (!modal) {
                console.error('模态框元素不存在');
                return;
            }

            // 显示模态框
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        }

        // 添加零跑日志表格排序相关变量
        let leapLogSort = {
            field: null,
            direction: 'asc'
        };
        let leapLogData = [];

        // 格式化显示值的函数
        function formatValue(val) {
            if (val === null || val === undefined) return '-';
            if (typeof val === 'number') {
                // 处理数值类型，保留4位小数
                return Number.isFinite(val) ? val.toFixed(4) : '-';
            }
            return val.toString();
        }

        // 显示完整测试log
        async function showLeapFullLog(serialNumber, testTime, station) {
            console.log('showLeapFullLog 被调用', serialNumber, testTime, station);
            if (!serialNumber || !testTime || !station) {
                alert('showLeapFullLog参数缺失：' + JSON.stringify({serialNumber, testTime, station}));
                return;
            }
            try {
                // 解析时间并调整格式
                const testDate = new Date(testTime);
                // 格式化为 YYYY/MM/DD HH:mm:ss
                const formattedTime = `${testDate.getFullYear()}/${String(testDate.getMonth() + 1).padStart(2, '0')}/${String(testDate.getDate()).padStart(2, '0')} ${String(testDate.getHours()).padStart(2, '0')}:${String(testDate.getMinutes()).padStart(2, '0')}:${String(testDate.getSeconds()).padStart(2, '0')}`;

                console.log('请求参数:', {
                    serialNumber,
                    testTime: formattedTime,
                    station
                });

                // 显示加载提示
                $('#fullLogModalContent').html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载数据...</div></div>');
                const modal = new bootstrap.Modal(document.getElementById('fullLogModal'));
                modal.show();

                const response = await fetch(`/api/lp8155/log_detail?serial_number=${encodeURIComponent(serialNumber)}&test_time=${encodeURIComponent(formattedTime)}&station=${encodeURIComponent(station)}`);
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('服务器返回的不是JSON格式数据');
                }

                const data = await response.json();
                console.log('解析后的数据:', data);

                if (data.error) {
                    throw new Error(data.error);
                }

                if (!Array.isArray(data)) {
                    throw new Error('返回的数据格式不正确，期望数组类型');
                }

                // 保存数据到全局变量
                leapLogData = data;

                let html = `
                    <div class="table-responsive">
                        <div class="mb-3">
                            <div><strong>序列号：</strong>${serialNumber}</div>
                            <div><strong>测试时间：</strong>${formattedTime}</div>
                            <div><strong>站位：</strong>${station}</div>
                        </div>
                        <table class="table table-bordered table-hover" style="background-color: white;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th class="text-center" style="width: 5%">序号</th>
                                    <th colspan="2" style="width: 47%" class="leap-log-sortable" data-field="Test Seq">Test Seq - step_name<span class="sort-icon"></span></th>
                                    <th style="width: 12%" class="leap-log-sortable" data-field="StandardValue">StandardValue<span class="sort-icon"></span></th>
                                    <th style="width: 12%" class="leap-log-sortable" data-field="MeasuredValue">MeasuredValue<span class="sort-icon"></span></th>
                                    <th style="width: 8%" class="leap-log-sortable" data-field="LowLimit">LowLimit<span class="sort-icon"></span></th>
                                    <th style="width: 8%" class="leap-log-sortable" data-field="HighLimit">HighLimit<span class="sort-icon"></span></th>
                                    <th style="width: 8%" class="leap-log-sortable" data-field="Result">Result<span class="sort-icon"></span></th>
                                </tr>
                            </thead>
                            <tbody>
                            ${data.map((item, index) => {
                                const isPass = item.Result?.toLowerCase() === 'pass';
                                const resultClass = isPass ? 'text-success' : 'text-danger';
                                const rowClass = index % 2 === 0 ? 'background-color: #f8f9fa;' : 'background-color: white;';
                                
                                return `<tr style="${rowClass}">
                                    <td class="text-center">${index + 1}</td>
                                    <td colspan="2" title="${formatValue(item['Test Seq'])}-${formatValue(item.step_name)}" style="max-width: 450px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item['Test Seq'])}-${formatValue(item.step_name)}</td>
                                    <td title="${formatValue(item.StandardValue)}" style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.StandardValue)}</td>
                                    <td title="${formatValue(item.MeasuredValue)}" style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.MeasuredValue)}</td>
                                    <td title="${formatValue(item.LowLimit)}" style="max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.LowLimit)}</td>
                                    <td title="${formatValue(item.HighLimit)}" style="max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.HighLimit)}</td>
                                    <td class="${resultClass}" style="font-weight: bold;">${formatValue(item.Result)?.toLowerCase()}</td>
                                </tr>`;
                            }).join('')}
                            </tbody>
                        </table>
                    </div>`;

                // 更新模态框内容和样式
                $('#fullLogModalContent').html(html);
                $('#fullLogModal .modal-dialog').addClass('modal-xl');
                $('#fullLogModal .modal-body').css({
                    'padding': '15px',
                    'background-color': 'white'
                });

            } catch (error) {
                console.error('获取测试log失败:', error);
                $('#fullLogModalContent').html(`
                    <div class="alert alert-danger">
                        <h5>获取测试log失败</h5>
                        <p>${error.message}</p>
                        <pre class="mt-2 small">${error.stack}</pre>
                    </div>
                `);
            }
        }

        // 零跑日志表格排序函数
        function sortLeapLogData() {
            if (!leapLogData || !leapLogSort.field) return;

            const sortedData = [...leapLogData].sort((a, b) => {
                let valueA = a[leapLogSort.field];
                let valueB = b[leapLogSort.field];

                // 处理特殊字段的排序
                if (['StandardValue', 'MeasuredValue', 'LowLimit', 'HighLimit'].includes(leapLogSort.field)) {
                    // 数值类型排序，先尝试转换为数字
                    valueA = parseFloat(valueA) || 0;
                    valueB = parseFloat(valueB) || 0;
                } else if (leapLogSort.field === 'Test Seq') {
                    // Test Seq可能包含字母和数字，需要特殊处理
                    valueA = valueA || '';
                    valueB = valueB || '';
                } else if (leapLogSort.field === 'Result') {
                    // Result排序，PASS排在前面
                    valueA = (valueA || '').toLowerCase() === 'pass' ? 0 : 1;
                    valueB = (valueB || '').toLowerCase() === 'pass' ? 0 : 1;
                }

                if (valueA < valueB) return leapLogSort.direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return leapLogSort.direction === 'asc' ? 1 : -1;
                return 0;
            });

            // 重新渲染表格内容
            const tbody = $('#fullLogModal tbody');
            tbody.empty();
            
            sortedData.forEach((item, index) => {
                const isPass = item.Result?.toLowerCase() === 'pass';
                const resultClass = isPass ? 'text-success' : 'text-danger';
                const rowClass = index % 2 === 0 ? 'background-color: #f8f9fa;' : 'background-color: white;';
                
                const tr = `<tr style="${rowClass}">
                    <td class="text-center">${index + 1}</td>
                    <td colspan="2" title="${formatValue(item['Test Seq'])}-${formatValue(item.step_name)}" style="max-width: 450px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item['Test Seq'])}-${formatValue(item.step_name)}</td>
                    <td title="${formatValue(item.StandardValue)}" style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.StandardValue)}</td>
                    <td title="${formatValue(item.MeasuredValue)}" style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.MeasuredValue)}</td>
                    <td title="${formatValue(item.LowLimit)}" style="max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.LowLimit)}</td>
                    <td title="${formatValue(item.HighLimit)}" style="max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${formatValue(item.HighLimit)}</td>
                    <td class="${resultClass}" style="font-weight: bold;">${formatValue(item.Result)?.toLowerCase()}</td>
                </tr>`;
                tbody.append(tr);
            });
        }

        // 在document ready中添加事件监听
        $(document).ready(function() {
            // ... 保持现有的代码 ...

            // 添加零跑日志表格排序事件监听
            $(document).on('click', '#fullLogModal .leap-log-sortable', function() {
                const field = $(this).data('field');
                if (leapLogSort.field === field) {
                    leapLogSort.direction = leapLogSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    leapLogSort.field = field;
                    leapLogSort.direction = 'asc';
                }
                
                // 更新所有排序图标
                $('#fullLogModal .sort-icon').removeClass('sort-asc sort-desc');
                $(this).find('.sort-icon').addClass(leapLogSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
                
                sortLeapLogData();
            });
        });

        // 零跑项目tab自定义日期显示逻辑
        $(document).ready(function() {
            $('#leapTimeRange').change(function() {
                const isCustom = $(this).val() === 'custom';
                $('#leapCustomDateInputs').toggle(isCustom);

                // 时间范围改变时，自动更新测试总览
                setTimeout(() => {
                    loadLeapDbForSummary();
                }, 100);
            });

            // 测试站位下拉框逻辑
            $('#leapTestStationSelect').change(function() {
                const testStation = $(this).val();
                const eolSelect = $('#leapEolSelect');

                // 清空现有选项
                eolSelect.empty();
                eolSelect.append('<option value="">全部设备</option>');

                if (testStation === 'SOC') {
                    // SOC测试：显示EOL1, EOL2, EOL3, EOL5
                    eolSelect.append('<option value="EOL1">EOL1</option>');
                    eolSelect.append('<option value="EOL2">EOL2</option>');
                    eolSelect.append('<option value="EOL3">EOL3</option>');
                    eolSelect.append('<option value="EOL5">EOL5</option>');
                } else if (testStation === 'VIU') {
                    // VIU测试：只显示EOL4
                    eolSelect.append('<option value="EOL4">EOL4</option>');
                }
            });

            // 测试结果过滤器变化时，不需要更新测试总览
            // 因为测试总览应该显示所有数据的统计，不受结果筛选影响
            // $('#leapResultFilter').change(function() {
            //     loadLeapDbForSummary();
            // });

            // 自定义时间范围变化时，自动更新测试总览
            $('#leapCustomDateStart, #leapCustomDateEnd').change(function() {
                if ($('#leapTimeRange').val() === 'custom') {
                    loadLeapDbForSummary();
                }
            });

            // 默认选中"SOC测试"并触发change事件来初始化EOL设备选项
            $('#leapTestStationSelect').val('SOC').trigger('change');
            $('#leapTimeRange').val('0');
            $('#leapResultFilter').val('');

            // 默认加载所有数据来统计SOC和VIU测试
            loadLeapDbForSummary();

            // 默认加载零跑项目数据
            loadLeapDb();
        });

        // 修复零跑项目表格排序和分页
        function sortAndDisplayLeapData() {
            if (!window.leapData || !window.leapData.length || !leapSortState.field) return;
            const sortedData = [...window.leapData].sort((a, b) => {
                let valueA = a[leapSortState.field];
                let valueB = b[leapSortState.field];
                if (leapSortState.field === 'TestTime') {
                    valueA = new Date(valueA).getTime();
                    valueB = new Date(valueB).getTime();
                }
                if (valueA < valueB) return leapSortState.direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return leapSortState.direction === 'asc' ? 1 : -1;
                return 0;
            });
            window.leapData = sortedData;
            currentLeapPage = 1;
            updateLeapTable(currentLeapPage);
        }

        // 分页渲染函数，始终用window.leapData
        function updateLeapTable(page = 1) {
            const LEAP_PAGE_SIZE = 10;
            const data = window.leapData || [];
            const start = (page - 1) * LEAP_PAGE_SIZE;
            const end = start + LEAP_PAGE_SIZE;
            const pageData = data.slice(start, end);
            let html = '';
            if (pageData && pageData.length > 0) {
                pageData.forEach(row => {
                    // TestTime减8小时
                    let dateObj = new Date(row.TestTime);
                    dateObj.setHours(dateObj.getHours() - 8);
                    let displayTime = `${dateObj.getFullYear()}/${(dateObj.getMonth()+1).toString().padStart(2,'0')}/${dateObj.getDate().toString().padStart(2,'0')} ${dateObj.getHours().toString().padStart(2,'0')}:${dateObj.getMinutes().toString().padStart(2,'0')}:${dateObj.getSeconds().toString().padStart(2,'0')}`;
                    // Failure_Item只显示第一个分号前的内容的第二个字段
                    let failureRaw = row.Failure_item || row.failure_item;
                    let failureItem = '-';
                    let failureEscaped = escapeJsString(failureRaw || '');
                    if (failureRaw) {
                        let firstEntry = failureRaw.split(';')[0].trim();
                        let arr = firstEntry.split(',');
                        if (arr.length > 1) failureItem = arr[1];
                        else failureItem = firstEntry;
                    }
                    let resultClass = '';
                    if (row.TestResult === 'Passed' || row.TestResult === 'Pass' || row.TestResult === 'PASSED') resultClass = 'text-success';
                    else if (row.TestResult === 'Failed' || row.TestResult === 'Fail' || row.TestResult === 'FAILED') resultClass = 'text-danger';
                    else resultClass = 'text-warning'; // 其他结果归为Other
                    // 直接用a标签包裹TestResult，onclick调用showLeapFullLog
                    html += `<tr>
                        <td>${displayTime}</td>
                        <td>${row.Serial_Number}</td>
                        <td><a href="javascript:void(0)" class="${resultClass}" style="cursor:pointer;text-decoration:underline;" onclick="showLeapFullLog('${row.Serial_Number}','${displayTime}','${row.Station}')">${row.TestResult}</a></td>
                        <td>${row.Station || '-'}</td>
                        <td style="max-width:300px;word-break:break-all;">
                            ${failureItem === '-' ? '-' : `<a href=\"javascript:void(0)\" onclick=\"showFailureDetail('${failureEscaped}')\">${failureItem}</a>`}
                        </td>
                    </tr>`;
                });
            } else {
                html = '<tr><td colspan="5">无数据</td></tr>';
            }
            $('#leap-tableBody').html(html);
            // 更新分页
            totalLeapPages = Math.ceil(data.length / LEAP_PAGE_SIZE);
            updateLeapPagination(page, totalLeapPages);
        }



        // 零跑项目测试总览和Station分布统计
        function updateLeapSummaryAndChart(data) {
            if (!Array.isArray(data) || data.length === 0) {
                // 清空所有统计数据
                $('#soc-totalTests, #soc-passCount, #soc-failCount, #soc-actualOutput').text(0);
                $('#soc-passRate').text('0.00');
                $('#viu-totalTests, #viu-passCount, #viu-failCount, #viu-actualOutput').text(0);
                $('#viu-passRate').text('0.00');
                if (window.leapStationChart) window.leapStationChart.clear();
                return;
            }

            // 分离SOC和VIU测试数据
            // 支持两种Station名称格式：带#号和不带#号
            const socData = data.filter(item => {
                const station = item.Station || '';
                return ['EOL1', 'EOL2', 'EOL3', 'EOL5', 'EOL#1', 'EOL#2', 'EOL#3', 'EOL#5'].includes(station);
            });

            const viuData = data.filter(item => {
                const station = item.Station || '';
                return ['EOL4', 'EOL#4'].includes(station);
            });

            // 统计SOC测试数据
            updateTestStats(socData, 'soc');

            // 统计VIU测试数据
            updateTestStats(viuData, 'viu');
            // 统计Station分布
            const stationMap = {};
            data.forEach(row => {
                const st = row.Station || '-';
                if (!stationMap[st]) stationMap[st] = {total: 0, pass: 0, fail: 0, other: 0};
                stationMap[st].total++;
                if (row.TestResult === 'Passed' || row.TestResult === 'Pass' || row.TestResult === 'PASSED') {
                    stationMap[st].pass++;
                } else if (row.TestResult === 'Failed' || row.TestResult === 'Fail' || row.TestResult === 'FAILED') {
                    stationMap[st].fail++;
                } else {
                    stationMap[st].other++; // 其他结果归为Other
                }
            });
            const stations = ['EOL1','EOL2','EOL3','EOL4','EOL5'];
            const totalArr = stations.map(s => stationMap[s]?.total || 0);
            const passArr = stations.map(s => stationMap[s]?.pass || 0);
            const failArr = stations.map(s => stationMap[s]?.fail || 0);
            const failRateArr = stations.map((s,i) => totalArr[i] ? (failArr[i]/totalArr[i]*100).toFixed(2) : 0);
            // 渲染echarts
            const chartDom = document.getElementById('leap-station-chart');
            if (!chartDom) return;
            if (window.leapStationChart) {
                window.leapStationChart.dispose();
            }
            window.leapStationChart = echarts.init(chartDom);
            const option = {
                tooltip: { trigger: 'axis' },
                legend: { data: ['通过', '失败', '失败率'] },
                grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                xAxis: [{ type: 'category', data: stations }],
                yAxis: [
                    { type: 'value', name: '数量' },
                    { type: 'value', name: '失败率(%)', max: 100 }
                ],
                series: [
                    { name: '通过', type: 'bar', stack: 'total', data: passArr },
                    { name: '失败', type: 'bar', stack: 'total', data: failArr },
                    { name: '失败率', type: 'line', yAxisIndex: 1, data: failRateArr }
                ]
            };
            window.leapStationChart.setOption(option);
            window.leapStationChart.resize();
        }

        // 辅助函数：更新测试统计数据
        function updateTestStats(data, prefix) {
            if (!Array.isArray(data) || data.length === 0) {
                $(`#${prefix}-totalTests, #${prefix}-passCount, #${prefix}-failCount, #${prefix}-actualOutput`).text(0);
                $(`#${prefix}-passRate`).text('0.00');
                return;
            }

            // 按序列号分组，计算FPY（首次通过率）
            const testsBySN = new Map();
            const allPassSN = new Set(); // 记录所有PASS状态的SN
            const sorted = [...data].sort((a, b) => new Date(a.TestTime) - new Date(b.TestTime));

            sorted.forEach(item => {
                if (!testsBySN.has(item.Serial_Number)) testsBySN.set(item.Serial_Number, []);
                testsBySN.get(item.Serial_Number).push(item);

                // 记录所有PASS状态的SN
                if (item.TestResult === 'Passed' || item.TestResult === 'Pass' || item.TestResult === 'PASSED') {
                    allPassSN.add(item.Serial_Number);
                }
            });

            // 统计每个SN的首次测试结果
            let firstPass = 0;
            let firstFail = 0;

            testsBySN.forEach(tests => {
                const first = tests[0];
                if (first) {
                    if (first.TestResult === 'Passed' || first.TestResult === 'Pass' || first.TestResult === 'PASSED') {
                        firstPass++;
                    } else if (first.TestResult === 'Failed' || first.TestResult === 'Fail' || first.TestResult === 'FAILED') {
                        firstFail++;
                    }
                    // 其他结果不计入首次通过率统计
                }
            });

            // 总数按SN去重计算
            const totalSN = testsBySN.size;
            const actualOutput = allPassSN.size; // 实际产出：所有PASS状态的去重SN数量
            const passRate = totalSN ? ((firstPass / totalSN) * 100).toFixed(2) : '0.00';

            // 更新页面显示 - 所有数据都按SN去重计算
            $(`#${prefix}-totalTests`).text(totalSN);
            $(`#${prefix}-passCount`).text(firstPass);
            $(`#${prefix}-failCount`).text(firstFail);
            $(`#${prefix}-passRate`).text(passRate);
            $(`#${prefix}-actualOutput`).text(actualOutput);
        }
        // 在零跑数据加载后自动刷新卡片
        function afterLeapDataLoaded(data) {
            updateLeapSummaryAndChart(data);
        }
        // 修改loadLeapDb和leapSnSearchBtn，数据加载后调用afterLeapDataLoaded
        const origLoadLeapDb = loadLeapDb;
        loadLeapDb = async function() {
            await origLoadLeapDb.apply(this, arguments);
            if (window.leapData) afterLeapDataLoaded(window.leapData);
        }
        $('#leapSnSearchBtn').off('click').on('click', function() {
            const snType = $('#leapSnTypeSelect').val();
            const snValue = $('#leapSnInput').val().trim();
            if (!snValue) { alert('请输入序列号'); return; }
            $('#leap-tableBody').html('<tr><td colspan="6">加载中...</td></tr>');
            fetch(`/api/leap8155/search_by_sn?sn_type=${snType}&sn_value=${encodeURIComponent(snValue)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) throw new Error(data.error);
                    window.leapData = data;
                    currentLeapPage = 1;
                    totalLeapPages = Math.ceil(data.length / LEAP_PAGE_SIZE);
                    updateLeapTable(currentLeapPage);
                    afterLeapDataLoaded(data);
                })
                .catch(error => {
                    console.error('序列号查询失败:', error);
                    $('#leap-tableBody').html(`<tr><td colspan="6" class="text-danger">查询失败: ${error.message}</td></tr>`);
                });
        });
        // 页面初次加载时也刷新
        $(document).ready(function(){
            if (window.leapData) afterLeapDataLoaded(window.leapData);
        });
        $(document).ready(function(){
            // 保证首次进入页面时Station分布图表能自动显示
            if (window.leapData && window.leapData.length > 0) {
                afterLeapDataLoaded(window.leapData);
            } else {
                // 监听loadLeapDb完成后自动刷新
                const origLoadLeapDb = loadLeapDb;
                loadLeapDb = async function() {
                    await origLoadLeapDb.apply(this, arguments);
                    if (window.leapData && window.leapData.length > 0) afterLeapDataLoaded(window.leapData);
                }
            }
            // 监听零跑项目tab切换，切换时延迟调用loadLeapDb
            $('#leap-tab').on('click', function() {
                setTimeout(() => {
                    // 重新初始化Top Issue图表（防止tab切换时图表未正确初始化）
                    if (!topIssueChart || topIssueChart.isDisposed()) {
                        topIssueChart = echarts.init(document.getElementById('topIssueChart'));
                    }
                    loadLeapDb();
                }, 200);
            });

            // 监听Bootstrap tab显示事件，确保图表正确渲染
            $('#leap-tab').on('shown.bs.tab', function() {
                setTimeout(() => {
                    if (!topIssueChart || topIssueChart.isDisposed()) {
                        const chartContainer = document.getElementById('topIssueChart');
                        if (chartContainer) {
                            topIssueChart = echarts.init(chartContainer);
                        }
                    }
                    if (topIssueChart) {
                        topIssueChart.resize();
                        // 如果有数据，重新渲染
                        if (topIssueData && topIssueData.length > 0) {
                            updateTopIssueChart();
                        }
                    }
                }, 300);
            });
        });

        // 烟花功能相关代码
        let fireworksActive = false;

        // ==================== Top Issue 分布相关函数 ====================

        // 加载Top Issue数据
        async function loadTopIssues() {
            try {
                showLoading();

                // 获取当前筛选条件（使用零跑项目的筛选器）
                const params = new URLSearchParams({
                    time_range: $('#leapTimeRange').val(),
                    station: $('#leapTestStationSelect').val(),
                    slave: $('#leapEolSelect').val() || '',
                    limit: $('#topIssueLimit').val()
                });

                // 如果是自定义时间，添加自定义日期参数
                if ($('#leapTimeRange').val() === 'custom') {
                    const startTime = $('#leapCustomDateStart').val();
                    const endTime = $('#leapCustomDateEnd').val();
                    if (startTime && endTime) {
                        params.append('custom_date_start', startTime);
                        params.append('custom_date_end', endTime);
                    }
                }

                const response = await fetch(`/api/lp8155/top_issues?${params}`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                topIssueData = data.issues || [];
                updateTopIssueChart();
                updateTopIssueTable();

            } catch (error) {
                console.error('加载Top Issue失败:', error);
                $('#topIssueChart').html(`<div class="text-center text-muted p-4">加载失败: ${error.message}</div>`);
                $('#topIssueTable').html(`<div class="text-center text-muted p-4">加载失败: ${error.message}</div>`);
            } finally {
                hideLoading();
            }
        }

        // 更新Top Issue图表
        function updateTopIssueChart() {
            if (!topIssueChart || !topIssueData || topIssueData.length === 0) {
                if (topIssueChart) {
                    topIssueChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                }
                return;
            }

            // 准备图表数据
            const chartData = topIssueData.map(item => ({
                name: item.failure_item.length > 30 ? item.failure_item.substring(0, 30) + '...' : item.failure_item,
                value: item.count,
                fullName: item.failure_item
            }));

            // 生成颜色数组
            const colors = [
                '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
                '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
                '#10ac84', '#ee5a24', '#0984e3', '#6c5ce7', '#a29bfe'
            ];

            const option = {
                title: {
                    text: 'Top Issue 分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return `${params.data.fullName}<br/>失败次数: ${params.value} (${params.percent}%)`;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    right: 10,
                    top: 20,
                    bottom: 20,
                    data: chartData.map(item => item.name),
                    textStyle: {
                        fontSize: 10
                    }
                },
                series: [
                    {
                        name: 'Top Issues',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['40%', '50%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: function(params) {
                                return `${params.data.fullName}\n${params.value} (${params.percent}%)`;
                            },
                            fontSize: 11,
                            lineHeight: 14
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '12',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true,
                            length: 15,
                            length2: 10,
                            lineStyle: {
                                width: 1
                            }
                        },
                        data: chartData.map((item, index) => ({
                            ...item,
                            itemStyle: {
                                color: colors[index % colors.length]
                            }
                        }))
                    }
                ]
            };

            topIssueChart.setOption(option);

            // 强制重新渲染图表
            setTimeout(() => {
                if (topIssueChart && !topIssueChart.isDisposed()) {
                    topIssueChart.resize();
                }
            }, 100);
        }

        // 更新Top Issue表格
        function updateTopIssueTable() {
            if (!topIssueData || topIssueData.length === 0) {
                $('#topIssueTable').html(`
                    <div class="text-center text-muted p-4">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <div>暂无失败项数据</div>
                    </div>
                `);
                return;
            }

            let tableHtml = `
                <table class="table table-sm table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 30px;">#</th>
                            <th style="width: auto;">失败项</th>
                            <th style="width: 50px;">次数</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            topIssueData.forEach((item, index) => {
                const badgeClass = index < 3 ? 'bg-danger' : index < 10 ? 'bg-warning' : 'bg-secondary';
                tableHtml += `
                    <tr>
                        <td style="padding: 4px 8px;">
                            <span class="badge ${badgeClass}" style="font-size: 10px;">${index + 1}</span>
                        </td>
                        <td style="padding: 4px 8px;">
                            <div class="failure-item-clickable"
                                 data-failure-item="${item.failure_item}"
                                 style="word-break: break-all; font-size: 12px; line-height: 1.3; cursor: pointer; color: #0066cc;"
                                 title="点击查看详细分布: ${item.failure_item}">
                                ${item.failure_item}
                            </div>
                        </td>
                        <td style="padding: 4px 8px; text-align: center;">
                            <strong class="text-primary" style="font-size: 14px;">${item.count}</strong>
                        </td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            $('#topIssueTable').html(tableHtml);

            // 绑定失败项点击事件
            $('.failure-item-clickable').off('click').on('click', function() {
                const failureItem = $(this).data('failure-item');
                console.log('点击失败项:', failureItem);
                console.log('调用showTopIssueDetail函数');
                showTopIssueDetail(failureItem);
            });

            console.log('失败项点击事件已绑定，共', $('.failure-item-clickable').length, '个元素');
        }

        // ==================== AI分析相关函数 ====================

        // 查询DeepSeek余额
        async function checkDeepSeekBalance() {
            try {
                const response = await fetch('/api/deepseek/balance');
                const data = await response.json();

                if (data.success) {
                    return data.balance;
                } else {
                    console.error('余额查询失败:', data.error);
                    return null;
                }
            } catch (error) {
                console.error('余额查询异常:', error);
                return null;
            }
        }

        // 显示收费确认对话框
        async function showPaymentConfirmation() {
            const balance = await checkDeepSeekBalance();

            let balanceText = '无法获取余额信息';
            if (balance) {
                if (balance.balance_infos && balance.balance_infos.length > 0) {
                    const balanceInfo = balance.balance_infos[0];
                    const totalBalance = balanceInfo.total_balance || '0.00';
                    balanceText = `余额信息: ${totalBalance}元`;
                } else if (balance.total_balance !== undefined) {
                    balanceText = `余额信息: ${balance.total_balance}元`;
                } else if (balance.available_balance !== undefined) {
                    balanceText = `余额信息: ${balance.available_balance}元`;
                } else {
                    balanceText = '余额信息: 0.00元';
                }
            }

            return new Promise((resolve) => {
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">💰 AI分析收费提示</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> 功能说明</h6>
                                    <p>AI智能分析功能使用DeepSeek API，会产生少量费用。</p>
                                </div>
                                <div class="mb-3">
                                    <strong>API密钥:</strong> sk-b28e0b5d4412410db203c87809ccb9ad<br>
                                    <strong>${balanceText}</strong>
                                </div>
                                <div class="alert alert-warning">
                                    <small>
                                        <i class="fas fa-exclamation-triangle"></i>
                                        每次分析大约消耗 0.001-0.01元
                                    </small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="confirmAnalysis">
                                    <i class="fas fa-check"></i> 确认并继续分析
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                const bsModal = new bootstrap.Modal(modal);

                modal.querySelector('#confirmAnalysis').onclick = () => {
                    bsModal.hide();
                    resolve(true);
                };

                modal.addEventListener('hidden.bs.modal', () => {
                    document.body.removeChild(modal);
                    resolve(false);
                });

                bsModal.show();
            });
        }

        // 执行AI分析
        async function performAIAnalysis() {
            // 记录AI分析访问日志
            try {
                await fetch('/api/log_ai_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'ai_analysis_start',
                        timestamp: new Date().toISOString(),
                        parameters: {
                            time_range: $('#leapTimeRange').val(),
                            station: $('#leapTestStationSelect').val(),
                            slave: $('#leapEolSelect').val() || '',
                            limit: $('#topIssueLimit').val()
                        }
                    })
                });
            } catch (e) {
                console.log('访问日志记录失败:', e);
            }

            // 显示收费确认对话框
            const confirmed = await showPaymentConfirmation();
            if (!confirmed) {
                // 记录取消操作
                try {
                    await fetch('/api/log_ai_analysis', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'ai_analysis_cancelled',
                            timestamp: new Date().toISOString()
                        })
                    });
                } catch (e) {
                    console.log('访问日志记录失败:', e);
                }
                return;
            }

            try {
                // 显示AI分析卡片
                $('#aiAnalysisCard').show();
                $('#aiAnalysisContent').html(`
                    <div class="text-center p-4">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">AI分析中...</span>
                        </div>
                        <p class="mt-2 text-muted">AI正在分析数据，请稍候...</p>
                    </div>
                `);

                // 获取当前筛选条件
                const params = new URLSearchParams({
                    time_range: $('#leapTimeRange').val(),
                    station: $('#leapTestStationSelect').val(),
                    slave: $('#leapEolSelect').val() || '',
                    limit: $('#topIssueLimit').val()
                });

                // 如果是自定义时间，添加自定义日期参数
                if ($('#leapTimeRange').val() === 'custom') {
                    const startTime = $('#leapCustomDateStart').val();
                    const endTime = $('#leapCustomDateEnd').val();
                    if (startTime && endTime) {
                        params.append('custom_date_start', startTime);
                        params.append('custom_date_end', endTime);
                    }
                }

                // 检查是否启用客户端AI调用
                const configResponse = await fetch('/api/ai_config');
                const configData = await configResponse.json();

                console.log('🔍 检查AI配置:', configData);

                // 默认使用客户端AI调用（因为服务器无网络）
                console.log('🌐 使用客户端AI分析');
                await performClientSideAIAnalysis(params);

            } catch (error) {
                console.error('AI分析失败:', error);

                // 记录失败日志
                try {
                    await fetch('/api/log_ai_analysis', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'ai_analysis_failed',
                            timestamp: new Date().toISOString(),
                            error: error.message
                        })
                    });
                } catch (e) {
                    console.log('访问日志记录失败:', e);
                }

                $('#aiAnalysisContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        AI分析失败: ${error.message}
                    </div>
                `);
            }
        }

        // 客户端AI分析
        async function performClientSideAIAnalysis(params) {
            try {
                console.log('🔧 开始客户端AI分析，参数:', params.toString());

                // 1. 获取AI配置
                console.log('⚙️  步骤1: 获取AI配置...');
                const configResponse = await fetch('/api/ai_config');
                const configData = await configResponse.json();
                const config = configData.config;
                console.log('✅ AI配置获取成功:', config);

                // 2. 获取分析数据
                console.log('📊 步骤2: 获取分析数据...');
                const dataResponse = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                const analysisData = await dataResponse.json();
                console.log('📈 分析数据响应:', analysisData);

                if (!analysisData.success) {
                    throw new Error(analysisData.error || '获取分析数据失败');
                }

                console.log('✅ 分析数据获取成功:', {
                    total_tests: analysisData.data.statistics.total_tests,
                    failed_tests: analysisData.data.statistics.failed_tests,
                    top_issues_count: analysisData.data.top_issues.length
                });

                // 3. 构建分析提示
                console.log('🤖 步骤3: 构建AI提示...');
                const prompt = buildAnalysisPrompt(analysisData.data);
                console.log('✅ 提示构建成功，长度:', prompt.length);

                // 4. 调用DeepSeek API
                console.log('🌐 调用DeepSeek API...');
                const aiResult = await callDeepSeekAPI(config, prompt);
                console.log('✅ DeepSeek API调用成功，结果长度:', aiResult.length);

                // 5. 解析并显示结果
                console.log('📋 步骤5: 解析并显示结果...');
                const analysis = parseAIResponse(aiResult);
                console.log('✅ 结果解析成功:', analysis);

                displayAIAnalysisResult({
                    success: true,
                    analysis: analysis,
                    data_source: '客户端AI分析',
                    generated_at: new Date().toLocaleString()
                });

                // 记录成功日志
                try {
                    await fetch('/api/log_ai_analysis', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'ai_analysis_completed',
                            timestamp: new Date().toISOString(),
                            data_source: '客户端AI分析',
                            analysis_summary: analysis.summary?.substring(0, 100) || '分析完成'
                        })
                    });
                } catch (e) {
                    console.log('访问日志记录失败:', e);
                }

                console.log('🎉 客户端AI分析完成！');

            } catch (error) {
                console.error('客户端AI分析失败:', error);
                $('#aiAnalysisContent').html(`
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> 客户端AI分析失败</h6>
                        <p class="mb-0">${error.message}</p>
                        <small class="text-muted">请检查网络连接和AI配置</small>
                    </div>
                `);
            }
        }

        // 构建分析提示
        function buildAnalysisPrompt(data) {
            console.log('构建分析提示，数据结构:', data);

            // 处理不同的数据结构
            let top_issues, detailed_failures, total_tests, failed_tests, time_info;

            if (data.statistics) {
                // 新的数据结构
                total_tests = parseInt(data.statistics.total_tests) || 0;
                failed_tests = parseInt(data.statistics.failed_tests) || 0;
                top_issues = data.top_issues || [];
                detailed_failures = data.detailed_failures || [];
                time_info = data.summary?.analysis_period || '未知时间范围';
            } else {
                // 旧的数据结构
                total_tests = data.total_tests || 0;
                failed_tests = data.failed_tests || 0;
                top_issues = data.top_issues || [];
                detailed_failures = [];
                time_info = data.time_info || '未知时间范围';
            }

            let prompt = `请分析以下汽车EOL测试数据：\n\n`;
            prompt += `## 基础统计信息\n`;
            prompt += `时间范围: ${time_info}\n`;
            prompt += `总测试数: ${total_tests}\n`;
            prompt += `失败测试数: ${failed_tests}\n`;

            if (total_tests > 0) {
                prompt += `失败率: ${((failed_tests / total_tests) * 100).toFixed(2)}%\n\n`;
            } else {
                prompt += `失败率: 0.00%\n\n`;
            }

            prompt += `## Top失败项分布\n`;
            if (top_issues && top_issues.length > 0) {
                top_issues.forEach((issue, index) => {
                    const percentage = parseFloat(issue.percentage) || 0;
                    prompt += `${index + 1}. ${issue.failure_item}: ${issue.count}次 (${percentage.toFixed(2)}%)\n`;
                });
            } else {
                prompt += `暂无失败项数据\n`;
            }

            // 添加详细失败数据分析
            if (detailed_failures && detailed_failures.length > 0) {
                prompt += `\n## 详细失败数据分析\n`;
                detailed_failures.forEach((failure, index) => {
                    prompt += `\n### ${index + 1}. ${failure.failure_item} (${failure.count}次失败，占比${parseFloat(failure.percentage).toFixed(2)}%)\n`;

                    if (failure.details && failure.details.length > 0) {
                        prompt += `具体失败案例:\n`;
                        failure.details.slice(0, 5).forEach((detail, detailIndex) => {
                            prompt += `  案例${detailIndex + 1}: ${detail.sub_item}\n`;
                            if (detail.measured_value !== null && detail.standard_value !== null) {
                                prompt += `    - 标准值: ${detail.standard_value}, 测试值: ${detail.measured_value}\n`;
                            }
                            if (detail.low_limit !== null && detail.high_limit !== null) {
                                prompt += `    - 限值范围: ${detail.low_limit} ~ ${detail.high_limit}\n`;
                            }
                            prompt += `    - 失败原因: ${detail.failure_reason}\n`;
                            prompt += `    - 测试时间: ${detail.test_time}\n`;
                        });
                    }
                });
            }

            prompt += `\n## 分析要求\n`;
            prompt += `请基于以上详细的失败数据，提供专业的分析报告，包括：\n`;
            prompt += `1. 数据概览和关键发现（重点分析具体失败原因和模式）\n`;
            prompt += `2. 失败趋势分析（基于测试值偏差情况）\n`;
            prompt += `3. 根本原因分析（结合具体测试项和失败模式）\n`;
            prompt += `4. 优先级建议（基于失败频次和影响程度）\n`;
            prompt += `5. 具体改进措施建议（针对每个主要失败项）\n\n`;
            prompt += `请用中文回答，格式清晰，重点突出，并结合具体的测试数据进行分析。`;

            return prompt;
        }

        // 调用客户端AI服务
        async function callClientSideAI(config, prompt) {
            const provider = config.provider;

            if (provider === 'deepseek') {
                return await callDeepSeekAPI(config, prompt);
            } else if (provider === 'openai') {
                return await callOpenAIAPI(config, prompt);
            } else {
                throw new Error(`不支持的客户端AI提供商: ${provider}`);
            }
        }

        // 调用DeepSeek API
        async function callDeepSeekAPI(config, prompt) {
            console.log('调用DeepSeek API，配置:', config);
            console.log('提示内容长度:', prompt.length);

            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer sk-b28e0b5d4412410db203c87809ccb9ad`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: config.model || 'deepseek-chat',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。'
                            },
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: config.max_tokens || 4000,
                        temperature: config.temperature || 0.3
                    })
                });

                console.log('DeepSeek API响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('DeepSeek API错误响应:', errorText);
                    throw new Error(`DeepSeek API调用失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('DeepSeek API响应数据:', data);

                if (data.choices && data.choices.length > 0) {
                    return data.choices[0].message.content;
                } else {
                    throw new Error('DeepSeek API返回数据格式异常');
                }
            } catch (error) {
                console.error('DeepSeek API调用异常:', error);
                throw error;
            }
        }

        // 调用OpenAI API
        async function callOpenAIAPI(config, prompt) {
            if (!config.api_key) {
                throw new Error('OpenAI API密钥未配置');
            }

            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.api_key}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: config.model || 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: config.max_tokens || 2000,
                    temperature: config.temperature || 0.3
                })
            });

            if (!response.ok) {
                throw new Error(`OpenAI API调用失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        }

        // 解析AI响应
        function parseAIResponse(aiResponse) {
            console.log('🔍 解析AI响应，长度:', aiResponse.length);

            // 直接使用AI的完整响应，不进行复杂解析
            // 尝试从AI响应中提取结构化信息
            const lines = aiResponse.split('\n').filter(line => line.trim());

            // 查找关键部分
            let summary = '';
            let keyFindings = [];
            let trendAnalysis = '';
            let rootCauseAnalysis = '';
            let priorityRecommendations = [];
            let improvementSuggestions = [];

            // 简单的文本解析
            let currentSection = '';
            for (let line of lines) {
                const trimmedLine = line.trim();

                if (trimmedLine.includes('概览') || trimmedLine.includes('摘要') || trimmedLine.includes('总结')) {
                    currentSection = 'summary';
                    continue;
                } else if (trimmedLine.includes('关键发现') || trimmedLine.includes('主要发现')) {
                    currentSection = 'findings';
                    continue;
                } else if (trimmedLine.includes('趋势分析') || trimmedLine.includes('失败趋势')) {
                    currentSection = 'trend';
                    continue;
                } else if (trimmedLine.includes('根本原因') || trimmedLine.includes('根因分析')) {
                    currentSection = 'root_cause';
                    continue;
                } else if (trimmedLine.includes('优先级') || trimmedLine.includes('建议')) {
                    currentSection = 'recommendations';
                    continue;
                } else if (trimmedLine.includes('改进措施') || trimmedLine.includes('改进建议')) {
                    currentSection = 'improvements';
                    continue;
                }

                // 根据当前部分添加内容
                if (trimmedLine && !trimmedLine.match(/^#+\s/)) { // 忽略markdown标题
                    switch (currentSection) {
                        case 'summary':
                            summary += trimmedLine + ' ';
                            break;
                        case 'findings':
                            if (trimmedLine.match(/^\d+\./) || trimmedLine.match(/^[-*]\s/)) {
                                keyFindings.push(trimmedLine.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, ''));
                            }
                            break;
                        case 'trend':
                            trendAnalysis += trimmedLine + ' ';
                            break;
                        case 'root_cause':
                            rootCauseAnalysis += trimmedLine + ' ';
                            break;
                        case 'recommendations':
                            if (trimmedLine.match(/^\d+\./) || trimmedLine.match(/^[-*]\s/)) {
                                priorityRecommendations.push(trimmedLine.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, ''));
                            }
                            break;
                        case 'improvements':
                            if (trimmedLine.match(/^\d+\./) || trimmedLine.match(/^[-*]\s/)) {
                                improvementSuggestions.push(trimmedLine.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, ''));
                            }
                            break;
                    }
                }
            }

            // 如果解析失败，使用原始响应
            if (!summary && keyFindings.length === 0) {
                summary = aiResponse.substring(0, 300) + (aiResponse.length > 300 ? '...' : '');
                keyFindings = ['AI分析已完成，请查看完整分析内容'];
            }

            return {
                summary: summary.trim() || aiResponse.substring(0, 200) + '...',
                key_findings: keyFindings.length > 0 ? keyFindings : ['AI分析已完成，请查看完整分析内容'],
                trend_analysis: trendAnalysis.trim() || '请查看完整分析内容中的趋势分析部分',
                root_cause_analysis: rootCauseAnalysis.trim() || '请查看完整分析内容中的根因分析部分',
                priority_recommendations: priorityRecommendations.length > 0 ? priorityRecommendations : ['请查看完整分析内容中的建议部分'],
                improvement_suggestions: improvementSuggestions.length > 0 ? improvementSuggestions : ['请查看完整分析内容中的改进措施部分'],
                full_response: aiResponse, // 保存完整响应
                data_insights: {
                    analysis_confidence: 'high',
                    total_issues_analyzed: keyFindings.length,
                    trend_direction: 'analyzed'
                }
            };
        }

        // 显示AI分析结果
        function displayAIAnalysisResult(data) {
            const analysis = data.analysis;
            const generatedAt = data.generated_at;

            $('#aiAnalysisTime').text(`生成时间: ${generatedAt}`);

            let contentHtml = `
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-lightbulb"></i> 分析摘要</h6>
                            <p class="mb-0">${analysis.summary}</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-warning mb-3">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-search"></i> 关键发现</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
            `;

            analysis.key_findings.forEach(finding => {
                contentHtml += `<li class="mb-2"><i class="fas fa-arrow-right text-warning"></i> ${finding}</li>`;
            });

            contentHtml += `
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-info mb-3">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> 趋势分析</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">${analysis.trend_analysis}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger mb-3">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0"><i class="fas fa-microscope"></i> 根因分析</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">${analysis.root_cause_analysis}</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-success mb-3">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-tasks"></i> 优先级建议</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
            `;

            analysis.priority_recommendations.forEach(recommendation => {
                contentHtml += `<li class="mb-2"><i class="fas fa-star text-success"></i> ${recommendation}</li>`;
            });

            contentHtml += `
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-tools"></i> 改进建议</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
            `;

            analysis.improvement_suggestions.forEach(suggestion => {
                contentHtml += `<li class="mb-2"><i class="fas fa-cog text-primary"></i> ${suggestion}</li>`;
            });

            contentHtml += `
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加完整AI响应显示
            if (analysis.full_response) {
                contentHtml += `
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-robot"></i> 完整AI分析报告
                                        <button class="btn btn-sm btn-outline-light float-end" onclick="toggleFullResponse()">
                                            <span id="toggleText">展开</span>
                                        </button>
                                    </h6>
                                </div>
                                <div class="card-body" id="fullResponseBody" style="display: none;">
                                    <pre style="white-space: pre-wrap; font-size: 14px; line-height: 1.5;">${analysis.full_response}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 如果有数据洞察信息，添加到底部
            if (analysis.data_insights) {
                const insights = analysis.data_insights;
                contentHtml += `
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-light">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    分析了 ${insights.total_issues_analyzed} 个问题项 |
                                    分析可信度: ${insights.analysis_confidence} |
                                    趋势方向: ${insights.trend_direction}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            }

            $('#aiAnalysisContent').html(contentHtml);
        }

        // 切换完整响应显示
        function toggleFullResponse() {
            const body = $('#fullResponseBody');
            const toggleText = $('#toggleText');

            if (body.is(':visible')) {
                body.slideUp();
                toggleText.text('展开');
            } else {
                body.slideDown();
                toggleText.text('收起');
            }
        }

        // ==================== AI配置相关函数 ====================

        // 显示AI配置模态框
        async function showAIConfigModal() {
            try {
                $('#aiConfigModal').modal('show');

                // 加载当前配置
                const response = await fetch('/api/ai_config');
                const data = await response.json();

                if (data.success) {
                    displayAIConfigForm(data);
                } else {
                    throw new Error(data.error || '加载配置失败');
                }

            } catch (error) {
                console.error('加载AI配置失败:', error);
                $('#aiConfigContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        加载配置失败: ${error.message}
                    </div>
                `);
            }
        }

        // 显示AI配置表单
        function displayAIConfigForm(data) {
            const config = data.config;

            // 固定使用DeepSeek，简化配置
            const configStatus = '<span class="badge bg-success">DeepSeek已就绪</span>';

            $('#aiConfigContent').html(`
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-robot"></i> DeepSeek AI智能分析</h6>
                            <p class="mb-1">• 系统已集成DeepSeek AI，提供专业的测试数据分析</p>
                            <p class="mb-1">• 默认启用客户端网络调用，适用于服务器无网络环境</p>
                            <p class="mb-0">• 当前状态: ${configStatus}</p>
                        </div>
                    </div>
                </div>

                <form id="aiConfigForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="aiModel" class="form-label">DeepSeek模型</label>
                                <select class="form-select" id="aiModel" name="model">
                                    <option value="deepseek-chat">deepseek-chat (推荐)</option>
                                    <option value="deepseek-reasoner">deepseek-reasoner</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">API密钥</label>
                                <input type="text" class="form-control" value="sk-b28e0b5d4412410db203c87809ccb9ad" readonly>
                                <div class="form-text text-success">已预配置DeepSeek API密钥</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="clientSideAI" name="client_side_ai" checked>
                                <label class="form-check-label" for="clientSideAI">
                                    <i class="fas fa-desktop"></i> <strong>客户端网络AI调用</strong>
                                </label>
                                <div class="form-text">默认启用，AI请求从客户端浏览器发起，适用于服务器无网络环境</div>
                            </div>
                        </div>
                    </div>
                </form>
            `);

            // 绑定事件
            $('#aiProvider').change(function() {
                updateModelOptions($(this).val(), providers);
            });

            $('#toggleApiKey').click(function() {
                const input = $('#aiApiKey');
                const icon = $(this).find('i');
                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    input.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
        }

        // 更新模型选项
        function updateModelOptions(provider, providers) {
            const providerConfig = providers[provider];
            let modelOptions = '';

            if (providerConfig) {
                providerConfig.models.forEach(model => {
                    const selected = model === providerConfig.default_model ? 'selected' : '';
                    modelOptions += `<option value="${model}" ${selected}>${model}</option>`;
                });
            }

            $('#aiModel').html(modelOptions);
        }

        // 保存AI配置
        async function saveAIConfig() {
            try {
                const formData = new FormData(document.getElementById('aiConfigForm'));
                const configData = {};

                for (let [key, value] of formData.entries()) {
                    if (key === 'cache_enabled') {
                        configData[key] = true;
                    } else if (key === 'max_tokens' || key === 'cache_duration_hours') {
                        configData[key] = parseFloat(value);
                    } else if (key === 'temperature') {
                        configData[key] = parseFloat(value);
                    } else {
                        configData[key] = value;
                    }
                }

                // 如果没有勾选缓存，设置为false
                if (!configData.cache_enabled) {
                    configData.cache_enabled = false;
                }

                const response = await fetch('/api/ai_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configData)
                });

                const result = await response.json();

                if (result.success) {
                    alert('AI配置保存成功！');
                    $('#aiConfigModal').modal('hide');
                } else {
                    throw new Error(result.error || '保存配置失败');
                }

            } catch (error) {
                console.error('保存AI配置失败:', error);
                alert('保存配置失败: ' + error.message);
            }
        }

        // ==================== 失败项详情相关函数 ====================

        let stationDistributionChart = null;
        let timeDistributionChart = null;
        let scatterChart = null;

        // 全局模态框清理函数
        function cleanupModalBackdrop() {
            console.log('执行模态框清理');
            // 移除所有可能残留的backdrop
            $('.modal-backdrop').remove();
            // 恢复body的正常状态
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
            $('body').css('overflow', '');
            // 确保页面可以正常滚动和交互
            $(document).off('touchmove.modal');
        }

        // 页面加载时清理可能残留的模态框状态
        $(document).ready(function() {
            cleanupModalBackdrop();

            // 监听所有模态框的关闭事件
            $(document).on('hidden.bs.modal', '.modal', function() {
                console.log('检测到模态框关闭事件');
                setTimeout(cleanupModalBackdrop, 100);
            });

            // 添加键盘快捷键：Ctrl+Alt+C 清理界面
            $(document).on('keydown', function(e) {
                if (e.ctrlKey && e.altKey && e.key === 'c') {
                    console.log('检测到清理快捷键 Ctrl+Alt+C');
                    cleanupModalBackdrop();
                    // 显示提示
                    if (typeof showToast === 'function') {
                        showToast('界面已清理', 'success');
                    } else {
                        alert('界面已清理');
                    }
                }
            });
        });

        // 显示失败项详情
        async function showTopIssueDetail(failureItem) {
            try {
                console.log('显示失败项详情:', failureItem);

                // 清理可能存在的旧模态框backdrop
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');

                // 更新模态框标题
                $('#topIssueDetailModalLabel').text(`失败项详细分布: ${failureItem}`);

                // 先设置加载状态
                $('#stationDistributionChart').html('<div class="text-center p-4"><div class="spinner-border text-primary"></div><div class="mt-2">加载设备分布...</div></div>');
                $('#timeDistributionChart').html('<div class="text-center p-4"><div class="spinner-border text-primary"></div><div class="mt-2">加载时间分布...</div></div>');
                $('#failureDetailTable').html('<div class="text-center p-4"><div class="spinner-border text-primary"></div><div class="mt-2">加载详细记录...</div></div>');

                // 显示模态框
                const modalElement = document.getElementById('topIssueDetailModal');

                // 确保模态框元素存在
                if (!modalElement) {
                    throw new Error('模态框元素不存在');
                }

                // 创建模态框实例，添加错误处理
                let modal;
                try {
                    modal = new bootstrap.Modal(modalElement, {
                        backdrop: true,
                        keyboard: true,
                        focus: true
                    });
                } catch (modalError) {
                    console.error('创建模态框实例失败:', modalError);
                    throw new Error('无法创建模态框');
                }

                // 添加模态框关闭事件监听
                modalElement.addEventListener('hidden.bs.modal', function() {
                    console.log('模态框已关闭，清理资源');
                    // 确保清理backdrop和body类
                    setTimeout(() => {
                        $('.modal-backdrop').remove();
                        $('body').removeClass('modal-open');
                        $('body').css('padding-right', '');
                    }, 100);
                });

                modal.show();

                // 等待模态框完全显示
                await new Promise(resolve => {
                    const handleShown = () => {
                        modalElement.removeEventListener('shown.bs.modal', handleShown);
                        console.log('模态框已完全显示');
                        resolve();
                    };
                    modalElement.addEventListener('shown.bs.modal', handleShown);

                    // 备用超时机制，防止无限等待
                    setTimeout(() => {
                        modalElement.removeEventListener('shown.bs.modal', handleShown);
                        console.log('模态框显示超时，继续执行');
                        resolve();
                    }, 2000);
                });

                console.log('开始加载数据...');

                // 获取当前筛选条件
                const params = new URLSearchParams({
                    failure_item: failureItem,
                    time_range: $('#leapTimeRange').val(),
                    station: $('#leapTestStationSelect').val(),
                    slave: $('#leapEolSelect').val() || ''
                });

                // 如果是自定义时间，添加自定义日期参数
                if ($('#leapTimeRange').val() === 'custom') {
                    const startTime = $('#leapCustomDateStart').val();
                    const endTime = $('#leapCustomDateEnd').val();
                    if (startTime && endTime) {
                        params.append('custom_date_start', startTime);
                        params.append('custom_date_end', endTime);
                    }
                }

                const url = `/api/lp8155/failure_detail?${params}`;
                console.log('失败项详情请求URL:', url);

                // 添加超时控制和更好的错误处理
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                    console.log('请求超时，已取消');
                }, 15000); // 15秒超时

                let response, data;
                try {
                    response = await fetch(url, {
                        signal: controller.signal,
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    });
                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    data = await response.json();
                    console.log('失败项详情API响应:', data);

                    if (data.error) {
                        throw new Error(data.error);
                    }

                } catch (fetchError) {
                    clearTimeout(timeoutId);
                    if (fetchError.name === 'AbortError') {
                        throw new Error('请求超时，请检查网络连接后重试');
                    } else if (fetchError.name === 'TypeError') {
                        throw new Error('网络连接失败，请检查网络后重试');
                    } else {
                        throw fetchError;
                    }
                }

                // 渲染图表和表格
                console.log('开始渲染图表和表格...');
                console.log('设备分布数据:', data.station_distribution);
                console.log('时间分布数据:', data.time_distribution);
                console.log('详细记录数据:', data.details);

                // 使用更稳定的渲染方式
                await renderFailureDetailCharts(data);

            } catch (error) {
                console.error('获取失败项详情失败:', error);

                // 显示错误信息，但保持模态框打开状态
                const errorMessage = error.message || '未知错误';
                $('#stationDistributionChart').html(`
                    <div class="text-center text-danger p-4">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                        <strong>加载失败</strong><br>
                        <small>${errorMessage}</small><br>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="showTopIssueDetail('${failureItem}')">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                    </div>
                `);
                $('#timeDistributionChart').html(`
                    <div class="text-center text-danger p-4">
                        <i class="fas fa-exclamation-triangle"></i><br>
                        加载失败: ${errorMessage}
                    </div>
                `);
                $('#failureDetailTable').html(`
                    <div class="text-center text-danger p-4">
                        <i class="fas fa-exclamation-triangle"></i><br>
                        加载失败: ${errorMessage}
                    </div>
                `);

                // 确保即使出错也不会影响模态框的正常关闭
                console.log('错误处理完成，模态框仍可正常使用');
            }
        }

        // 统一的图表渲染函数
        async function renderFailureDetailCharts(data) {
            console.log('开始统一渲染失败项详情图表');

            try {
                // 确保容器存在
                const stationContainer = document.getElementById('stationDistributionChart');
                const timeContainer = document.getElementById('timeDistributionChart');
                const tableContainer = document.getElementById('failureDetailTable');

                if (!stationContainer || !timeContainer || !tableContainer) {
                    console.error('图表容器不存在');
                    throw new Error('图表容器不存在');
                }

                // 并行渲染所有组件
                const renderPromises = [
                    renderStationDistributionChart(data.station_distribution || []),
                    renderTimeDistributionChart(data.time_distribution || []),
                    renderScatterChart(data.details || []),
                    renderFailureDetailTable(data.details || [], data.total_occurrences || 0)
                ];

                await Promise.all(renderPromises);
                console.log('所有图表渲染完成');

            } catch (error) {
                console.error('图表渲染过程中出错:', error);
                throw error;
            }
        }

        // 渲染设备分布图表
        async function renderStationDistributionChart(stationData) {
            return new Promise((resolve, reject) => {
                try {
                    console.log('开始渲染设备分布图表，数据:', stationData);

                    const chartContainer = document.getElementById('stationDistributionChart');
                    if (!chartContainer) {
                        console.error('设备分布图表容器不存在');
                        $('#stationDistributionChart').html('<div class="text-center text-muted p-4">图表容器不存在</div>');
                        resolve();
                        return;
                    }

                    // 检查数据
                    if (!stationData || stationData.length === 0) {
                        console.log('没有设备分布数据');
                        $('#stationDistributionChart').html('<div class="text-center text-muted p-4"><i class="fas fa-info-circle"></i><br>暂无设备分布数据</div>');
                        resolve();
                        return;
                    }

                    // 安全地销毁旧图表
                    try {
                        if (stationDistributionChart && !stationDistributionChart.isDisposed()) {
                            stationDistributionChart.dispose();
                            console.log('销毁旧的设备分布图表');
                        }
                    } catch (disposeError) {
                        console.warn('销毁设备分布图表时出错:', disposeError);
                        // 强制重置图表变量
                        stationDistributionChart = null;
                    }

                    // 等待一下再创建新图表
                    setTimeout(() => {
                        try {
                            // 创建新图表
                            stationDistributionChart = echarts.init(chartContainer);
                            console.log('创建新的设备分布图表');

                            const option = {
                                title: {
                                    text: '设备分布',
                                    left: 'center',
                                    textStyle: { fontSize: 14, fontWeight: 'bold' }
                                },
                                tooltip: {
                                    trigger: 'item',
                                    formatter: '{b}: {c} ({d}%)'
                                },
                                legend: {
                                    orient: 'vertical',
                                    left: 'left',
                                    top: 'middle'
                                },
                                series: [{
                                    type: 'pie',
                                    radius: ['40%', '70%'],
                                    center: ['60%', '50%'],
                                    data: stationData.map((item, index) => ({
                                        name: item.station || item.Station || '未知设备',
                                        value: item.count || item.Count || 0,
                                        itemStyle: {
                                            color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][index % 5]
                                        }
                                    })),
                                    itemStyle: {
                                        borderRadius: 5,
                                        borderColor: '#fff',
                                        borderWidth: 2
                                    },
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }]
                            };

                            stationDistributionChart.setOption(option);
                            console.log('设备分布图表选项设置完成');

                            // 确保图表渲染完成
                            setTimeout(() => {
                                if (stationDistributionChart && !stationDistributionChart.isDisposed()) {
                                    stationDistributionChart.resize();
                                    console.log('设备分布图表大小调整完成');
                                }
                                resolve();
                            }, 100);

                        } catch (innerError) {
                            console.error('创建设备分布图表时出错:', innerError);
                            $('#stationDistributionChart').html(`<div class="text-center text-danger p-4">渲染失败: ${innerError.message}</div>`);
                            reject(innerError);
                        }
                    }, 100);

                } catch (error) {
                    console.error('渲染设备分布图表失败:', error);
                    $('#stationDistributionChart').html(`<div class="text-center text-danger p-4">渲染失败: ${error.message}</div>`);
                    reject(error);
                }
            });
        }

        // 渲染时间分布图表
        async function renderTimeDistributionChart(timeData) {
            return new Promise((resolve, reject) => {
                try {
                    console.log('开始渲染时间分布图表，数据:', timeData);

                    const chartContainer = document.getElementById('timeDistributionChart');
                    if (!chartContainer) {
                        console.error('时间分布图表容器不存在');
                        $('#timeDistributionChart').html('<div class="text-center text-muted p-4">图表容器不存在</div>');
                        resolve();
                        return;
                    }

                    // 检查数据
                    if (!timeData || timeData.length === 0) {
                        console.log('没有时间分布数据');
                        $('#timeDistributionChart').html('<div class="text-center text-muted p-4"><i class="fas fa-info-circle"></i><br>暂无时间分布数据</div>');
                        resolve();
                        return;
                    }

                    // 安全地销毁旧图表
                    try {
                        if (timeDistributionChart && !timeDistributionChart.isDisposed()) {
                            timeDistributionChart.dispose();
                            console.log('销毁旧的时间分布图表');
                        }
                    } catch (disposeError) {
                        console.warn('销毁时间分布图表时出错:', disposeError);
                        // 强制重置图表变量
                        timeDistributionChart = null;
                    }

                    // 等待一下再创建新图表
                    setTimeout(() => {
                        try {
                            // 创建新图表
                            timeDistributionChart = echarts.init(chartContainer);
                            console.log('创建新的时间分布图表');

                            const option = {
                                title: {
                                    text: '时间分布',
                                    left: 'center',
                                    textStyle: { fontSize: 14, fontWeight: 'bold' }
                                },
                                tooltip: {
                                    trigger: 'axis',
                                    formatter: function(params) {
                                        return `时间: ${params[0].axisValue}<br/>失败次数: ${params[0].value}`;
                                    }
                                },
                                grid: {
                                    left: '3%',
                                    right: '4%',
                                    bottom: '3%',
                                    containLabel: true
                                },
                                xAxis: {
                                    type: 'category',
                                    data: timeData.map(item => item.hour || item.time || item.Time || '未知时间'),
                                    axisLabel: {
                                        rotate: 45,
                                        fontSize: 10
                                    },
                                    boundaryGap: false
                                },
                                yAxis: {
                                    type: 'value',
                                    name: '失败次数',
                                    nameTextStyle: {
                                        fontSize: 12
                                    }
                                },
                                series: [{
                                    type: 'line',
                                    data: timeData.map(item => item.count || item.Count || 0),
                                    smooth: true,
                                    symbol: 'circle',
                                    symbolSize: 6,
                                    lineStyle: {
                                        color: '#ff6b6b',
                                        width: 3
                                    },
                                    itemStyle: {
                                        color: '#ff6b6b'
                                    },
                                    areaStyle: {
                                        color: {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            colorStops: [{
                                                offset: 0, color: 'rgba(255, 107, 107, 0.5)'
                                            }, {
                                                offset: 1, color: 'rgba(255, 107, 107, 0.1)'
                                            }]
                                        }
                                    }
                                }]
                            };

                            timeDistributionChart.setOption(option);
                            console.log('时间分布图表选项设置完成');

                            // 确保图表渲染完成
                            setTimeout(() => {
                                if (timeDistributionChart && !timeDistributionChart.isDisposed()) {
                                    timeDistributionChart.resize();
                                    console.log('时间分布图表大小调整完成');
                                }
                                resolve();
                            }, 100);

                        } catch (innerError) {
                            console.error('创建时间分布图表时出错:', innerError);
                            $('#timeDistributionChart').html(`<div class="text-center text-danger p-4">渲染失败: ${innerError.message}</div>`);
                            reject(innerError);
                        }
                    }, 100);

                } catch (error) {
                    console.error('渲染时间分布图表失败:', error);
                    $('#timeDistributionChart').html(`<div class="text-center text-danger p-4">渲染失败: ${error.message}</div>`);
                    reject(error);
                }
            });
        }

        // 失败项详情表格排序状态
        let failureDetailSort = {
            field: null,
            direction: 'asc'
        };
        let failureDetailData = [];

        // 渲染详细记录表格
        async function renderFailureDetailTable(details, totalCount) {
            return new Promise((resolve) => {
                try {
                    console.log('开始渲染详细记录表格，数据:', details);

                    if (!details || details.length === 0) {
                        $('#failureDetailTable').html(`
                            <div class="text-center text-muted p-4">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <div>暂无详细记录</div>
                            </div>
                        `);
                        resolve();
                        return;
                    }

                    // 保存数据到全局变量用于排序
                    failureDetailData = details;

                    // 统计小项数量
                    const subItemsCount = new Set(details.map(detail => {
                        const parsed = parseFailureItemForTable(detail.Failure_item);
                        return parsed.subItem;
                    }).filter(item => item && item !== '-')).size;

                    let tableHtml = `
                        <div class="mb-3">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-chart-bar"></i> 总计: ${totalCount} 次失败
                            </span>
                            <span class="badge bg-info me-2">
                                <i class="fas fa-list"></i> 记录数: ${details.length}
                            </span>
                            <span class="badge bg-success">
                                <i class="fas fa-sitemap"></i> 测试项: ${subItemsCount} 个
                            </span>
                        </div>
                        <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th class="failure-detail-sortable" data-field="test_time" style="min-width: 140px; cursor: pointer; user-select: none;">
                                            测试时间 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="serial_number" style="min-width: 150px; cursor: pointer; user-select: none;">
                                            序列号 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="test_device" style="min-width: 100px; cursor: pointer; user-select: none;">
                                            测试设备 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="test_item" style="min-width: 200px; cursor: pointer; user-select: none;">
                                            测试项 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="standard_value" style="min-width: 100px; cursor: pointer; user-select: none;">
                                            标准值 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="measured_value" style="min-width: 100px; cursor: pointer; user-select: none;">
                                            测试值 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="low_limit" style="min-width: 100px; cursor: pointer; user-select: none;">
                                            下限 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="high_limit" style="min-width: 100px; cursor: pointer; user-select: none;">
                                            上限 <span class="sort-icon"></span>
                                        </th>
                                        <th class="failure-detail-sortable" data-field="result" style="min-width: 80px; cursor: pointer; user-select: none;">
                                            结果 <span class="sort-icon"></span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="failureDetailTableBody">
                    `;

                    // 渲染表格内容
                    tableHtml += renderFailureDetailRows(details);

                    tableHtml += `
                                </tbody>
                            </table>
                        </div>
                    `;

                    $('#failureDetailTable').html(tableHtml);

                    // 绑定排序事件
                    $('.failure-detail-sortable').off('click').on('click', function() {
                        const field = $(this).data('field');

                        // 更新排序状态
                        if (failureDetailSort.field === field) {
                            failureDetailSort.direction = failureDetailSort.direction === 'asc' ? 'desc' : 'asc';
                        } else {
                            failureDetailSort.field = field;
                            failureDetailSort.direction = 'asc';
                        }

                        // 更新排序图标
                        $('.failure-detail-sortable .sort-icon').removeClass('sort-asc sort-desc');
                        $(this).find('.sort-icon').addClass(failureDetailSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');

                        // 执行排序
                        sortFailureDetailData();
                    });

                    console.log('详细记录表格渲染完成');

                    // 添加表格加载完成的视觉反馈
                    setTimeout(() => {
                        resolve();
                    }, 50);

                } catch (error) {
                    console.error('渲染详细记录表格失败:', error);
                    $('#failureDetailTable').html(`
                        <div class="text-center text-danger p-4">
                            <i class="fas fa-exclamation-triangle"></i><br>
                            渲染失败: ${error.message}
                        </div>
                    `);
                    resolve();
                }
            });
        }

        // 渲染散点图
        async function renderScatterChart(details) {
            return new Promise((resolve) => {
                try {
                    console.log('开始渲染散点图，数据:', details);

                    const chartContainer = document.getElementById('scatterChart');
                    if (!chartContainer) {
                        console.error('散点图容器不存在');
                        resolve();
                        return;
                    }

                    // 初始化散点图
                    if (scatterChart) {
                        scatterChart.dispose();
                    }
                    scatterChart = echarts.init(chartContainer);

                    if (!details || details.length === 0) {
                        scatterChart.setOption({
                            title: {
                                text: '暂无数据',
                                left: 'center',
                                top: 'center',
                                textStyle: {
                                    color: '#999',
                                    fontSize: 16
                                }
                            }
                        });
                        resolve();
                        return;
                    }

                    // 渲染散点图（新版本）
                    renderScatterViewWithCheckboxes(details);

                    console.log('散点图渲染完成');
                    resolve();

                } catch (error) {
                    console.error('渲染散点图失败:', error);
                    $('#scatterChart').html(`<div class="text-center text-danger p-4">渲染失败: ${error.message}</div>`);
                    resolve();
                }
            });
        }

        // 渲染带复选框的散点图
        function renderScatterViewWithCheckboxes(details) {
            if (!scatterChart || !details || details.length === 0) return;

            // 解析数据
            const parsedData = details.map(item => {
                const parsed = parseFailureItemForScatter(item.Failure_item);
                return {
                    ...item,
                    parsedData: parsed
                };
            });

            // 获取所有小项列表
            const allSubItems = [...new Set(parsedData
                .filter(item => item.parsedData && item.parsedData.subItem)
                .map(item => item.parsedData.subItem)
            )].sort();

            // 生成单选框
            generateSubItemCheckboxes(allSubItems);

            // 初始渲染（默认显示第一个子项）
            if (allSubItems.length > 0) {
                renderFilteredScatterChart(parsedData, [allSubItems[0]]);
            }

            // 绑定单选框事件
            bindCheckboxEvents(parsedData, allSubItems);
        }

        // 解析Failure_item字段（散点图专用）
        function parseFailureItemForScatter(failureItem) {
            if (!failureItem) return null;

            const parts = failureItem.split(',');
            if (parts.length >= 6) {
                return {
                    mainItem: parts[0] || '',
                    subItem: parts[1] || '',
                    standardValue: parseFloat(parts[2]) || 0,
                    measuredValue: parseFloat(parts[3]) || 0,
                    lowLimit: parseFloat(parts[4]) || 0,
                    highLimit: parseFloat(parts[5]) || 0,
                    result: parts[6] || '',
                    time: parts[7] || ''
                };
            }
            return null;
        }

        // 生成子项单选框
        function generateSubItemCheckboxes(subItems) {
            const container = $('#subItemCheckboxes');
            container.empty();

            subItems.forEach((subItem, index) => {
                const radioId = `subItem_${index}`;
                const isFirst = index === 0; // 第一个选项默认选中
                const radio = $(`
                    <div class="form-check form-check-inline mb-2" style="min-width: 200px;">
                        <input class="form-check-input sub-item-radio" type="radio"
                               name="subItemSelection" id="${radioId}" value="${subItem}" ${isFirst ? 'checked' : ''}>
                        <label class="form-check-label" for="${radioId}"
                               style="font-size: 12px; line-height: 1.3; word-break: break-all; white-space: normal;"
                               title="${subItem}">
                            ${subItem}
                        </label>
                    </div>
                `);
                container.append(radio);
            });
        }

        // 绑定单选框事件
        function bindCheckboxEvents(parsedData, allSubItems) {
            $('.sub-item-radio').off('change').on('change', function() {
                const selectedValue = $('input[name="subItemSelection"]:checked').val();
                const selectedSubItems = [selectedValue]; // 只显示选中的子项

                renderFilteredScatterChart(parsedData, selectedSubItems);
            });
        }

        // 渲染过滤后的散点图
        function renderFilteredScatterChart(parsedData, selectedSubItems) {
            if (!scatterChart) return;

            // 过滤数据
            const filteredData = parsedData.filter(item =>
                item.parsedData && selectedSubItems.includes(item.parsedData.subItem)
            );

            if (filteredData.length === 0) {
                scatterChart.setOption({
                    title: {
                        text: '请选择要显示的测试项',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            color: '#999',
                            fontSize: 16
                        }
                    },
                    series: []
                });
                return;
            }

            // 按时间排序
            const sortedData = filteredData.sort((a, b) => {
                const timeA = new Date(a.Test_Time || a.TestTime || 0);
                const timeB = new Date(b.Test_Time || b.TestTime || 0);
                return timeA - timeB;
            });

            // 为每个小项分配颜色
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
            const subItemColors = {};
            selectedSubItems.forEach((subItem, index) => {
                subItemColors[subItem] = colors[index % colors.length];
            });

            // 准备散点数据
            const scatterData = selectedSubItems.map(subItem => {
                const subItemData = sortedData
                    .filter(item => item.parsedData.subItem === subItem)
                    .map((item) => {
                        const time = new Date(item.Test_Time || item.TestTime || 0);
                        const testValue = item.parsedData?.measuredValue || 0;
                        return [
                            time.getTime(),
                            testValue,
                            item
                        ];
                    });

                const sampleItem = sortedData.find(item =>
                    item.parsedData.subItem === subItem
                );
                const mainItem = sampleItem?.parsedData?.mainItem || '';
                const displayName = `${mainItem} - ${subItem}`;

                return {
                    name: displayName,
                    type: 'scatter',
                    symbolSize: 8,
                    data: subItemData,
                    itemStyle: {
                        color: subItemColors[subItem]
                    }
                };
            });

            // 添加上下限线
            const limitPairs = new Set();
            sortedData.forEach(item => {
                if (item.parsedData && item.parsedData.highLimit && item.parsedData.lowLimit) {
                    limitPairs.add(`${item.parsedData.lowLimit},${item.parsedData.highLimit}`);
                }
            });

            if (limitPairs.size > 0) {
                const firstValidData = sortedData.find(item => item.parsedData);
                const upperLimit = firstValidData?.parsedData?.highLimit || 0;
                const lowerLimit = firstValidData?.parsedData?.lowLimit || 0;

                const timeRange = [
                    Math.min(...sortedData.map(item => new Date(item.Test_Time || item.TestTime || 0).getTime())),
                    Math.max(...sortedData.map(item => new Date(item.Test_Time || item.TestTime || 0).getTime()))
                ];

                if (!isNaN(upperLimit) && upperLimit !== 0) {
                    scatterData.push({
                        name: '上限',
                        type: 'line',
                        data: [[timeRange[0], upperLimit], [timeRange[1], upperLimit]],
                        lineStyle: {
                            color: '#e74c3c',
                            width: 3,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        showSymbol: false,
                        z: 10
                    });
                }

                if (!isNaN(lowerLimit) && lowerLimit !== 0) {
                    scatterData.push({
                        name: '下限',
                        type: 'line',
                        data: [[timeRange[0], lowerLimit], [timeRange[1], lowerLimit]],
                        lineStyle: {
                            color: '#e74c3c',
                            width: 3,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        showSymbol: false,
                        z: 10
                    });
                }
            }

            const option = {
                title: {
                    text: '失败项测试值分布散点图',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.seriesName === '上限' || params.seriesName === '下限') {
                            return `${params.seriesName}: ${params.data[1]}`;
                        }

                        const item = params.data[2];
                        if (!item) return '';

                        const time = new Date(params.data[0]).toLocaleString('zh-CN');
                        const serialNumber = item.Serial_Number || '未知';
                        const device = item.Test_Device || item.Station || '未知设备';
                        const parsed = item.parsedData;
                        const testValue = parsed?.measuredValue || '未知';
                        const standardValue = parsed?.standardValue || '未知';
                        const upperLimit = parsed?.highLimit || '未知';
                        const lowerLimit = parsed?.lowLimit || '未知';
                        const mainItem = parsed?.mainItem || '未知大项';
                        const subItem = parsed?.subItem || '未知小项';

                        return `
                            <div style="text-align: left;">
                                <strong style="color: #333;">${mainItem}</strong><br/>
                                <span style="color: #666; font-size: 13px;">${subItem}</span><br/>
                                <hr style="margin: 8px 0; border-color: #eee;">
                                时间: ${time}<br/>
                                序列号: ${serialNumber}<br/>
                                设备: ${device}<br/>
                                <hr style="margin: 8px 0; border-color: #eee;">
                                测试值: <strong style="color: #e74c3c;">${testValue}</strong><br/>
                                标准值: ${standardValue}<br/>
                                上限: ${upperLimit}<br/>
                                下限: ${lowerLimit}
                            </div>
                        `;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    bottom: 10,
                    data: scatterData.map(item => item.name).filter(name => name !== '上限' && name !== '下限')
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    name: '时间',
                    nameLocation: 'middle',
                    nameGap: 30,
                    axisLabel: {
                        formatter: function(value) {
                            return echarts.format.formatTime('MM-dd\nhh:mm', value);
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '测试值',
                    nameLocation: 'middle',
                    nameGap: 40,
                    scale: true
                },
                series: scatterData,
                dataZoom: [
                    {
                        type: 'inside',
                        xAxisIndex: 0,
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        xAxisIndex: 0,
                        bottom: 50,
                        height: 20
                    }
                ]
            };

            scatterChart.setOption(option);
        }

        // 保留原有的时间视图散点图函数（备用）
        function renderTimeScatterView(details) {
            if (!scatterChart || !details || details.length === 0) return;

            // 处理数据：按时间排序并准备散点数据
            const sortedDetails = [...details].sort((a, b) => {
                const timeA = new Date(a.Test_Time || a.TestTime || 0);
                const timeB = new Date(b.Test_Time || b.TestTime || 0);
                return timeA - timeB;
            });

            // 解析Failure_item字段获取测试数据
            function parseFailureItem(failureItem) {
                if (!failureItem) return null;

                // 格式：section_2.11 HSD V3,DCSource InputCurrent JN6-6_USS-3,2.3,0.0,1.9,2.7,Fail,10.040724
                const parts = failureItem.split(',');
                if (parts.length >= 6) {
                    return {
                        mainItem: parts[0] || '',
                        subItem: parts[1] || '',
                        standardValue: parseFloat(parts[2]) || 0,
                        measuredValue: parseFloat(parts[3]) || 0,
                        lowLimit: parseFloat(parts[4]) || 0,
                        highLimit: parseFloat(parts[5]) || 0,
                        result: parts[6] || '',
                        time: parts[7] || ''
                    };
                }
                return null;
            }

            // 解析所有数据并提取测试值
            const parsedData = sortedDetails.map(item => {
                const parsed = parseFailureItem(item.Failure_item);
                return {
                    ...item,
                    parsedData: parsed
                };
            });

            // 获取所有小项列表
            const subItems = [...new Set(parsedData
                .filter(item => item.parsedData && item.parsedData.subItem)
                .map(item => item.parsedData.subItem)
            )].sort();

            // 为每个小项分配颜色（按大项分组）
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
            const mainItems = [...new Set(parsedData
                .filter(item => item.parsedData && item.parsedData.mainItem)
                .map(item => item.parsedData.mainItem)
            )];

            const subItemColors = {};
            subItems.forEach((subItem, index) => {
                // 找到这个小项属于哪个大项
                const itemData = parsedData.find(item =>
                    item.parsedData && item.parsedData.subItem === subItem
                );
                const mainItem = itemData?.parsedData?.mainItem || '';
                const mainItemIndex = mainItems.indexOf(mainItem);
                subItemColors[subItem] = colors[mainItemIndex % colors.length];
            });

            // 收集所有不同的上下限值
            const limitPairs = new Set();
            parsedData.forEach(item => {
                if (item.parsedData && item.parsedData.highLimit && item.parsedData.lowLimit) {
                    limitPairs.add(`${item.parsedData.lowLimit},${item.parsedData.highLimit}`);
                }
            });

            // 显示限值线（如果有有效的限值数据）
            let showLimitLines = limitPairs.size > 0;
            let upperLimit = 0, lowerLimit = 0;

            if (showLimitLines) {
                const firstValidData = parsedData.find(item => item.parsedData);
                upperLimit = firstValidData?.parsedData?.highLimit || 0;
                lowerLimit = firstValidData?.parsedData?.lowLimit || 0;

                // 如果有多组不同的限值，使用最常见的一组
                if (limitPairs.size > 1) {
                    console.log(`检测到${limitPairs.size}组不同的限值，使用第一组进行显示`);
                }
            }

            // 准备散点数据 - 按小项分组
            const scatterData = subItems.map(subItem => {
                const subItemData = parsedData
                    .filter(item => item.parsedData && item.parsedData.subItem === subItem)
                    .map((item) => {
                        const time = new Date(item.Test_Time || item.TestTime || 0);
                        const testValue = item.parsedData?.measuredValue || 0;
                        return [
                            time.getTime(),
                            testValue,
                            item
                        ];
                    });

                // 获取这个小项的大项名称用于显示
                const sampleItem = parsedData.find(item =>
                    item.parsedData && item.parsedData.subItem === subItem
                );
                const mainItem = sampleItem?.parsedData?.mainItem || '';
                const displayName = `${mainItem} - ${subItem}`;

                return {
                    name: displayName,
                    type: 'scatter',
                    symbolSize: 8,
                    data: subItemData,
                    itemStyle: {
                        color: subItemColors[subItem]
                    }
                };
            });

            // 添加上下限线（仅当所有数据使用相同限值时）
            if (showLimitLines) {
                const timeRange = [
                    Math.min(...sortedDetails.map(item => new Date(item.Test_Time || item.TestTime || 0).getTime())),
                    Math.max(...sortedDetails.map(item => new Date(item.Test_Time || item.TestTime || 0).getTime()))
                ];

                if (!isNaN(upperLimit) && upperLimit !== 0) {
                    scatterData.push({
                        name: '上限',
                        type: 'line',
                        data: [[timeRange[0], upperLimit], [timeRange[1], upperLimit]],
                        lineStyle: {
                            color: '#e74c3c',
                            width: 3,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        showSymbol: false,
                        z: 10
                    });
                }

                if (!isNaN(lowerLimit) && lowerLimit !== 0) {
                    scatterData.push({
                        name: '下限',
                        type: 'line',
                        data: [[timeRange[0], lowerLimit], [timeRange[1], lowerLimit]],
                        lineStyle: {
                            color: '#e74c3c',
                            width: 3,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        showSymbol: false,
                        z: 10
                    });
                }
            }

            const option = {
                title: {
                    text: '失败项测试值分布散点图',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        // 如果是上下限线，显示简单信息
                        if (params.seriesName === '上限' || params.seriesName === '下限') {
                            return `${params.seriesName}: ${params.data[1]}`;
                        }

                        const item = params.data[2];
                        if (!item) return '';

                        const time = new Date(params.data[0]).toLocaleString('zh-CN');
                        const serialNumber = item.Serial_Number || '未知';
                        const device = item.Test_Device || item.Station || '未知设备';
                        const parsed = item.parsedData;
                        const testValue = parsed?.measuredValue || '未知';
                        const standardValue = parsed?.standardValue || '未知';
                        const upperLimit = parsed?.highLimit || '未知';
                        const lowerLimit = parsed?.lowLimit || '未知';
                        const mainItem = parsed?.mainItem || '未知大项';
                        const subItem = parsed?.subItem || '未知小项';

                        return `
                            <div style="text-align: left;">
                                <strong style="color: #333;">${mainItem}</strong><br/>
                                <span style="color: #666; font-size: 13px;">${subItem}</span><br/>
                                <hr style="margin: 8px 0; border-color: #eee;">
                                时间: ${time}<br/>
                                序列号: ${serialNumber}<br/>
                                设备: ${device}<br/>
                                <hr style="margin: 8px 0; border-color: #eee;">
                                测试值: <strong style="color: #e74c3c;">${testValue}</strong><br/>
                                标准值: ${standardValue}<br/>
                                上限: ${upperLimit}<br/>
                                下限: ${lowerLimit}
                            </div>
                        `;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    bottom: 10,
                    data: scatterData.map(item => item.name).filter(name => name !== '上限' && name !== '下限')
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    name: '时间',
                    nameLocation: 'middle',
                    nameGap: 30,
                    axisLabel: {
                        formatter: function(value) {
                            return echarts.format.formatTime('MM-dd\nhh:mm', value);
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '测试值',
                    nameLocation: 'middle',
                    nameGap: 40,
                    scale: true
                },
                series: scatterData,
                dataZoom: [
                    {
                        type: 'inside',
                        xAxisIndex: 0,
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        xAxisIndex: 0,
                        bottom: 50,
                        height: 20
                    }
                ]
            };

            scatterChart.setOption(option);
        }

        // 渲染设备视图散点图
        function renderDeviceScatterView(details) {
            if (!scatterChart || !details || details.length === 0) return;

            // 获取设备列表和时间范围
            const devices = [...new Set(details.map(item =>
                item.Test_Device || item.Station || '未知设备'
            ))].sort();

            // 按设备分组数据
            const deviceGroups = {};
            devices.forEach(device => {
                deviceGroups[device] = details
                    .filter(item => (item.Test_Device || item.Station || '未知设备') === device)
                    .sort((a, b) => {
                        const timeA = new Date(a.Test_Time || a.TestTime || 0);
                        const timeB = new Date(b.Test_Time || b.TestTime || 0);
                        return timeA - timeB;
                    });
            });

            // 准备散点数据
            const scatterData = [];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];

            devices.forEach((device, deviceIndex) => {
                const deviceData = deviceGroups[device].map((item) => {
                    const time = new Date(item.Test_Time || item.TestTime || 0);
                    return [
                        deviceIndex,
                        time.getTime(),
                        item
                    ];
                });

                scatterData.push({
                    name: device,
                    type: 'scatter',
                    symbolSize: 8,
                    data: deviceData,
                    itemStyle: {
                        color: colors[deviceIndex % colors.length]
                    }
                });
            });

            const option = {
                title: {
                    text: '失败项设备分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const item = params.data[2];
                        if (!item) return '';

                        const time = new Date(params.data[1]).toLocaleString('zh-CN');
                        const serialNumber = item.Serial_Number || '未知';
                        const device = item.Test_Device || item.Station || '未知设备';
                        const parsed = item.parsedData;
                        const testValue = parsed?.measuredValue || '未知';
                        const standardValue = parsed?.standardValue || '未知';
                        const upperLimit = parsed?.highLimit || '未知';
                        const lowerLimit = parsed?.lowLimit || '未知';

                        return `
                            <div style="text-align: left;">
                                <strong>${params.seriesName}</strong><br/>
                                时间: ${time}<br/>
                                序列号: ${serialNumber}<br/>
                                设备: ${device}<br/>
                                测试值: ${testValue}<br/>
                                标准值: ${standardValue}<br/>
                                上限: ${upperLimit}<br/>
                                下限: ${lowerLimit}
                            </div>
                        `;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    bottom: 10,
                    data: devices
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: devices,
                    name: '测试设备',
                    nameLocation: 'middle',
                    nameGap: 30,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'time',
                    name: '时间',
                    nameLocation: 'middle',
                    nameGap: 50,
                    axisLabel: {
                        formatter: function(value) {
                            return echarts.format.formatTime('MM-dd hh:mm', value);
                        }
                    }
                },
                series: scatterData,
                dataZoom: [
                    {
                        type: 'inside',
                        yAxisIndex: 0,
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        yAxisIndex: 0,
                        right: 10,
                        width: 20
                    }
                ]
            };

            scatterChart.setOption(option);
        }

        // 解析Failure_item字段获取测试数据
        function parseFailureItemForTable(failureItem) {
            if (!failureItem) return null;

            // 格式：section_2.11 HSD V3,DCSource InputCurrent JN6-6_USS-3,2.3,0.0,1.9,2.7,Fail,10.040724
            const parts = failureItem.split(',');

            // 提取大项和小项
            const mainItem = parts[0] || '';  // section_2.11 HSD V3
            const subItem = parts[1] || '';   // DCSource InputCurrent JN6-6_USS-3

            if (parts.length >= 6) {
                return {
                    mainItem: mainItem,
                    subItem: subItem,
                    standardValue: parts[2] || '-',
                    measuredValue: parts[3] || '-',
                    lowLimit: parts[4] || '-',
                    highLimit: parts[5] || '-',
                    result: parts[6] || '-'
                };
            }
            return {
                mainItem: mainItem,
                subItem: subItem,
                standardValue: '-',
                measuredValue: '-',
                lowLimit: '-',
                highLimit: '-',
                result: '-'
            };
        }

        // 渲染失败项详情表格行
        function renderFailureDetailRows(details) {
            let rowsHtml = '';

            details.forEach((detail, index) => {
                try {
                    // 处理测试时间，减去8小时
                    let testTime = '未知时间';
                    const rawTime = detail.Test_Time || detail.TestTime;
                    if (rawTime) {
                        const originalDate = new Date(rawTime);
                        // 减去8小时（8 * 60 * 60 * 1000 毫秒）
                        const adjustedDate = new Date(originalDate.getTime() - 8 * 60 * 60 * 1000);
                        testTime = adjustedDate.toLocaleString('zh-CN');
                    }

                    const serialNumber = detail.Serial_Number || '未知序列号';
                    const testDevice = detail.Test_Device || detail.Station || '未知设备';
                    const failureItem = detail.Failure_item || '未知失败项';

                    // 解析失败项数据
                    const parsed = parseFailureItemForTable(failureItem);

                    const rowClass = index % 2 === 0 ? '' : 'table-light';

                    // 根据结果设置颜色
                    const resultBadgeClass = parsed.result === 'Fail' ? 'bg-danger' :
                                           parsed.result === 'Pass' ? 'bg-success' : 'bg-secondary';

                    rowsHtml += `
                        <tr class="${rowClass}">
                            <td style="font-size: 14px; padding: 12px 8px; white-space: nowrap;">${testTime}</td>
                            <td style="font-size: 14px; padding: 12px 8px;">${serialNumber}</td>
                            <td style="padding: 12px 8px;"><span class="badge bg-secondary" style="font-size: 12px;">${testDevice}</span></td>
                            <td style="font-size: 13px; padding: 12px 8px; word-break: break-all; line-height: 1.4;">
                                <div style="color: #666; font-size: 11px; margin-bottom: 2px;">${parsed.mainItem}</div>
                                <div style="color: #333; font-weight: 500;">${parsed.subItem || '-'}</div>
                            </td>
                            <td class="text-center" style="padding: 12px 8px; font-size: 14px;">${parsed.standardValue}</td>
                            <td class="text-center" style="padding: 12px 8px; font-size: 14px; font-weight: bold;">${parsed.measuredValue}</td>
                            <td class="text-center" style="padding: 12px 8px; font-size: 14px;">${parsed.lowLimit}</td>
                            <td class="text-center" style="padding: 12px 8px; font-size: 14px;">${parsed.highLimit}</td>
                            <td class="text-center" style="padding: 12px 8px;">
                                <span class="badge ${resultBadgeClass}" style="font-size: 12px;">${parsed.result}</span>
                            </td>
                        </tr>
                    `;
                } catch (itemError) {
                    console.warn('处理详细记录项时出错:', itemError, detail);
                }
            });

            return rowsHtml;
        }

        // 失败项详情表格排序函数
        function sortFailureDetailData() {
            if (!failureDetailData || !failureDetailSort.field) return;

            const sortedData = [...failureDetailData].sort((a, b) => {
                let valueA = getFailureDetailFieldValue(a, failureDetailSort.field);
                let valueB = getFailureDetailFieldValue(b, failureDetailSort.field);

                // 处理特殊字段的排序
                if (failureDetailSort.field === 'test_time') {
                    valueA = new Date(valueA).getTime() || 0;
                    valueB = new Date(valueB).getTime() || 0;
                } else if (['standard_value', 'measured_value', 'low_limit', 'high_limit'].includes(failureDetailSort.field)) {
                    valueA = parseFloat(valueA) || 0;
                    valueB = parseFloat(valueB) || 0;
                } else {
                    // 字符串排序
                    valueA = (valueA || '').toString().toLowerCase();
                    valueB = (valueB || '').toString().toLowerCase();
                }

                if (valueA < valueB) return failureDetailSort.direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return failureDetailSort.direction === 'asc' ? 1 : -1;
                return 0;
            });

            // 重新渲染表格内容
            $('#failureDetailTableBody').html(renderFailureDetailRows(sortedData));
        }

        // 获取失败项详情字段值
        function getFailureDetailFieldValue(item, field) {
            const parsed = parseFailureItemForTable(item.Failure_item);

            switch (field) {
                case 'test_time':
                    return item.Test_Time || item.TestTime || '';
                case 'serial_number':
                    return item.Serial_Number || '';
                case 'test_device':
                    return item.Test_Device || item.Station || '';
                case 'test_item':
                    return `${parsed.mainItem} - ${parsed.subItem}`;
                case 'standard_value':
                    return parseFloat(parsed.standardValue) || 0;
                case 'measured_value':
                    return parseFloat(parsed.measuredValue) || 0;
                case 'low_limit':
                    return parseFloat(parsed.lowLimit) || 0;
                case 'high_limit':
                    return parseFloat(parsed.highLimit) || 0;
                case 'result':
                    return parsed.result || '';
                default:
                    return '';
            }
        }

        // ==================== 烟花相关函数 ====================

        // 神秘入口点击事件
        $('#secretEntrance').click(function() {
            openFireworksModal();
        });

        // 关闭烟花按钮事件
        $('#fireworksCloseBtn').click(function() {
            closeFireworksModal();
        });

        // 打开烟花模态框
        function openFireworksModal() {
            $('#fireworksModal').addClass('active');
            fireworksActive = true;

            // 重新加载iframe以确保烟花效果正常
            const iframe = document.getElementById('fireworks-iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }

        // 关闭烟花模态框
        function closeFireworksModal() {
            $('#fireworksModal').removeClass('active');
            fireworksActive = false;
        }





        // 键盘事件处理
        $(document).on('keydown', function(e) {
            if (fireworksActive && e.key === 'Escape') {
                closeFireworksModal();
            }
        });

        // 防止模态框内容区域点击时关闭模态框
        $(document).on('click', '.fireworks-container', function(e) {
            e.stopPropagation();
        });

        // 版本管理功能
        function loadVersionInfo() {
            fetch('/api/version')
                .then(response => response.json())
                .then(data => {
                    $('#versionText').text(data.version);
                    $('#currentVersion').text(data.version);
                    $('#releaseDate').text(data.release_date);
                    $('#serviceStartTime').text(data.service_start_time);
                })
                .catch(error => {
                    console.error('获取版本信息失败:', error);
                });
        }

        // 版本徽章点击事件
        $('#versionBadge').click(function() {
            $('#versionModal').css('display', 'flex');
        });

        // 关闭版本弹窗
        function closeVersionModal() {
            $('#versionModal').css('display', 'none');
        }

        // 点击弹窗背景关闭
        $('#versionModal').click(function(e) {
            if (e.target === this) {
                closeVersionModal();
            }
        });

        // ESC键关闭版本弹窗
        $(document).keydown(function(e) {
            if (e.keyCode === 27 && $('#versionModal').css('display') === 'flex') {
                closeVersionModal();
            }
        });

        // 页面加载时获取版本信息
        loadVersionInfo();
    </script>
</body>
</html>