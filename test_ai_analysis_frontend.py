#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端AI分析完整流程
"""

import requests
import json
import time

def test_ai_analysis_flow(station_name, station_value):
    """测试完整的AI分析流程"""
    print(f"\n🤖 测试 {station_name} 的AI分析流程...")
    
    try:
        # 1. 获取分析数据
        print("  步骤1: 获取分析数据...")
        response = requests.get("http://localhost:5000/api/lp8155/ai_analysis_data", 
                              params={
                                  'time_range': '7',
                                  'station': station_value,
                                  'slave': '',
                                  'limit': '10'
                              })
        
        if not response.ok:
            print(f"  ❌ 数据获取失败: HTTP {response.status_code}")
            return False
            
        data = response.json()
        if not data.get('success'):
            print(f"  ❌ 数据获取失败: {data.get('error', '未知错误')}")
            return False
            
        analysis_data = data['data']
        print(f"  ✅ 数据获取成功: {analysis_data['total_tests']}个测试, {analysis_data['failed_tests']}个失败")
        
        # 2. 构建AI提示
        print("  步骤2: 构建AI提示...")
        prompt = build_analysis_prompt(analysis_data)
        print(f"  ✅ 提示构建成功，长度: {len(prompt)}")
        
        # 3. 调用DeepSeek API
        print("  步骤3: 调用DeepSeek API...")
        ai_result = call_deepseek_api(prompt)
        if ai_result:
            print(f"  ✅ AI分析成功，结果长度: {len(ai_result)}")
            print(f"  📝 AI分析摘要: {ai_result[:100]}...")
            return True
        else:
            print("  ❌ AI分析失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 流程异常: {str(e)}")
        return False

def build_analysis_prompt(data):
    """构建分析提示"""
    prompt = f"""
请分析以下零跑汽车EOL测试数据并提供专业的质量分析报告：

## 测试概况
最近7天共进行测试{data['total_tests']}次，其中失败{data['failed_tests']}次，失败率为{data['fail_rate']}%。

## Top失败项分布
"""
    
    for i, issue in enumerate(data['top_issues'][:5], 1):
        prompt += f"{i}. {issue['failure_item']}: {issue['count']}次\n"
    
    prompt += """

请提供：
1. **测试概况**: 对当前测试数据的整体评估
2. **关键发现**: 主要的质量问题和趋势分析  
3. **根因分析**: 对Top失败项的可能原因分析
4. **改进建议**: 具体的质量改进措施和优化方向

请用中文回答，格式清晰，重点突出。
"""
    
    return prompt

def call_deepseek_api(prompt):
    """调用DeepSeek API"""
    try:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer sk-b28e0b5d4412410db203c87809ccb9ad'
        }
        
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {'role': 'user', 'content': prompt}
            ],
            'max_tokens': 4000,
            'temperature': 0.3
        }
        
        response = requests.post(
            'https://api.deepseek.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"    DeepSeek API错误: HTTP {response.status_code}")
            print(f"    响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"    DeepSeek API异常: {str(e)}")
        return None

def main():
    print("🚀 开始测试前端AI分析完整流程")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试不同站位的AI分析
    stations = [
        ("全部站位", ""),
        ("SOC测试", "SOC"),
        ("VIU测试", "VIU")
    ]
    
    results = {}
    for station_name, station_value in stations:
        results[station_name] = test_ai_analysis_flow(station_name, station_value)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 AI分析流程测试结果总结:")
    
    success_count = 0
    for station_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {station_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 测试完成！成功率: {success_count}/{len(stations)} ({success_count/len(stations)*100:.1f}%)")
    
    if success_count == len(stations):
        print("🎉 所有站位的AI分析功能都正常工作！")
        print("💡 前端AI分析应该可以正常使用了")
    elif success_count > 0:
        print("⚠️ 部分站位的AI分析正常，可能需要进一步调试")
    else:
        print("❌ 所有AI分析都失败，需要检查配置和网络")

if __name__ == "__main__":
    main()
