#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的DeepSeek AI系统
"""

import requests
import json
import time

def test_simplified_deepseek():
    """测试简化后的DeepSeek系统"""
    print("🤖 测试简化DeepSeek AI系统")
    print("=" * 50)
    
    # 1. 测试AI配置
    print("\n📋 步骤1: 检查AI配置")
    print("-" * 30)
    
    try:
        response = requests.get('http://localhost:5000/api/ai_config')
        if response.status_code == 200:
            config_data = response.json()
            print("✅ 配置获取成功")
            print(f"   提供商: {config_data['config']['provider']}")
            print(f"   模型: {config_data['config']['model']}")
            print(f"   客户端AI: {config_data['config'].get('client_side_ai', False)}")
            print(f"   API密钥: {'已配置' if config_data['config'].get('api_key') else '未配置'}")
        else:
            print(f"❌ 配置获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False
    
    # 2. 测试数据获取
    print("\n📊 步骤2: 测试数据获取")
    print("-" * 30)
    
    try:
        response = requests.get(
            'http://localhost:5000/api/lp8155/ai_analysis_data',
            params={'time_range': '7', 'limit': '3'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("✅ 数据获取成功")
                analysis_data = data['data']
                print(f"   Top Issues: {len(analysis_data.get('top_issues', []))}")
                print(f"   总测试数: {analysis_data.get('total_tests', 0)}")
                print(f"   失败测试数: {analysis_data.get('failed_tests', 0)}")
            else:
                print(f"❌ 数据获取失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 数据请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False
    
    # 3. 测试服务器端AI分析（如果网络可用）
    print("\n🤖 步骤3: 测试服务器端AI分析")
    print("-" * 30)
    
    try:
        print("正在调用DeepSeek API...")
        start_time = time.time()
        
        response = requests.get(
            'http://localhost:5000/api/lp8155/ai_analysis',
            params={'time_range': '7', 'limit': '3'},
            timeout=120  # 增加客户端超时时间
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 服务器端AI分析成功 (耗时: {end_time - start_time:.2f}秒)")
                analysis = data['analysis']
                print(f"   分析置信度: {analysis['data_insights']['analysis_confidence']}")
                print(f"   关键发现数量: {len(analysis['key_findings'])}")
                
                # 显示部分分析结果
                if analysis.get('summary'):
                    summary_preview = analysis['summary'][:100] + "..." if len(analysis['summary']) > 100 else analysis['summary']
                    print(f"   📝 分析摘要: {summary_preview}")
                    
                print("\n🎯 服务器端AI分析功能正常")
                
            else:
                print(f"❌ 服务器端AI分析失败: {data.get('error')}")
                print("💡 建议使用客户端AI调用模式")
                
        else:
            print(f"❌ 服务器端AI分析请求失败: {response.status_code}")
            print("💡 建议使用客户端AI调用模式")
            
    except requests.exceptions.Timeout:
        print("⏰ 服务器端AI分析超时")
        print("💡 建议使用客户端AI调用模式")
    except Exception as e:
        print(f"❌ 服务器端AI分析异常: {e}")
        print("💡 建议使用客户端AI调用模式")
    
    # 4. 总结
    print(f"\n🎉 测试完成")
    print("=" * 50)
    print("✅ 系统已简化配置完成！")
    
    print("\n🔧 当前配置:")
    print("   • 固定使用DeepSeek AI提供商")
    print("   • API密钥已预配置")
    print("   • 默认启用客户端AI调用")
    print("   • 支持动态数据分析")
    
    print("\n📋 使用说明:")
    print("   1. 系统已默认配置DeepSeek AI")
    print("   2. 如果服务器网络不稳定，会自动提示使用客户端模式")
    print("   3. 在AI配置中可以启用'客户端网络AI调用'")
    print("   4. 客户端模式下AI请求从浏览器发起，绕过服务器网络限制")
    
    print("\n🚀 系统已就绪，可以开始使用AI分析功能！")
    
    return True

if __name__ == '__main__':
    success = test_simplified_deepseek()
    if success:
        print("\n✨ 简化DeepSeek系统测试完成！")
    else:
        print("\n❌ 测试过程中发现问题，请检查配置。")
