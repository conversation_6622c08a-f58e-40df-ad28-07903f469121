#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析服务 - 简化版
"""

import json
import requests
from datetime import datetime
from typing import Dict, Any, List

class AIAnalyzer:
    """AI分析器 - 简化版"""
    
    def __init__(self, get_db_connection):
        self.get_db_connection = get_db_connection
        self.api_key = 'sk-b28e0b5d4412410db203c87809ccb9ad'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'
    
    def analyze(self, time_range: str, station: str = '', slave: str = '', limit: int = 10) -> Dict[str, Any]:
        """执行AI分析"""
        try:
            print(f"🤖 开始AI分析，时间范围: {time_range}, 站位: {station}")
            
            # 1. 收集数据
            analysis_data = self._collect_analysis_data(time_range, station, slave, limit)
            
            # 2. 构建AI提示
            prompt = self._build_analysis_prompt(analysis_data)
            
            # 3. 调用AI服务
            ai_response = self._call_ai_service(prompt)
            
            return {
                'success': True,
                'analysis': ai_response,
                'data_summary': {
                    'total_tests': analysis_data.get('total_tests', 0),
                    'failed_tests': analysis_data.get('failed_tests', 0),
                    'fail_rate': analysis_data.get('fail_rate', 0),
                    'top_issues_count': len(analysis_data.get('top_issues', []))
                },
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            print(f"❌ AI分析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def _collect_analysis_data(self, time_range: str, station: str, slave: str, limit: int) -> Dict[str, Any]:
        """收集分析数据"""
        print(f"📊 收集分析数据...")
        
        # 构建时间条件
        time_condition, params = self._build_time_condition(time_range)
        
        # 构建站位条件
        station_condition = self._build_station_condition(station, params)
        
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 获取总体统计
            stats_query = f"""
            SELECT
                COUNT(*) as total_tests,
                SUM(CASE WHEN TestResult = 'Passed' THEN 1 ELSE 0 END) as passed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) as failed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as fail_rate
            FROM lp8155_eol_log
            WHERE 1=1 {time_condition} {station_condition}
            """
            
            cursor.execute(stats_query, params)
            stats = cursor.fetchone()
            
            # 获取Top Issues
            top_issues_query = f"""
            SELECT
                SUBSTRING_INDEX(Failure_item, ',', 1) as failure_item,
                COUNT(*) as count
            FROM lp8155_eol_log
            WHERE TestResult = 'Failed'
            AND Failure_item IS NOT NULL
            AND Failure_item != ''
            AND Failure_item != '-'
            {time_condition}
            {station_condition}
            GROUP BY SUBSTRING_INDEX(Failure_item, ',', 1)
            ORDER BY count DESC
            LIMIT %s
            """
            
            cursor.execute(top_issues_query, params + [limit])
            top_issues = cursor.fetchall()
            
            return {
                'total_tests': stats[0] if stats else 0,
                'passed_tests': stats[1] if stats else 0,
                'failed_tests': stats[2] if stats else 0,
                'fail_rate': round(stats[3], 2) if stats and stats[3] else 0,
                'top_issues': [{'failure_item': row[0], 'count': row[1]} for row in top_issues],
                'time_range': time_range,
                'station': station
            }
            
        finally:
            cursor.close()
            conn.close()
    
    def _build_time_condition(self, time_range: str):
        """构建时间条件"""
        if time_range == '0':  # 今天
            return " AND DATE(TestTime) = CURDATE()", []
        elif time_range == '1':  # 昨天
            return " AND DATE(TestTime) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)", []
        elif time_range == '7':  # 最近7天
            return " AND TestTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)", []
        elif time_range == '30':  # 最近30天
            return " AND TestTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)", []
        else:
            return "", []
    
    def _build_station_condition(self, station: str, params: list):
        """构建站位条件"""
        if station == 'SOC':
            # SOC测试：EOL1, EOL2, EOL3, EOL5 (支持带#号和不带#号)
            return " AND Station IN ('EOL1', 'EOL2', 'EOL3', 'EOL5', 'EOL#1', 'EOL#2', 'EOL#3', 'EOL#5')"
        elif station == 'VIU':
            # VIU测试：EOL4 (支持带#号和不带#号)
            return " AND Station IN ('EOL4', 'EOL#4')"
        elif station:
            # 其他特定站位
            params.append(station)
            return " AND Station = %s"
        return ""
    
    def _build_analysis_prompt(self, data: Dict[str, Any]) -> str:
        """构建AI分析提示"""
        prompt = f"""
请分析以下测试数据并提供专业的质量分析报告：

## 数据概览
- 测试总数: {data['total_tests']}
- 通过测试: {data['passed_tests']}
- 失败测试: {data['failed_tests']}
- 失败率: {data['fail_rate']}%
- 时间范围: {data['time_range']}天
- 测试站位: {data['station'] or '全部'}

## Top失败项
"""
        
        for i, issue in enumerate(data['top_issues'][:5], 1):
            prompt += f"{i}. {issue['failure_item']}: {issue['count']}次\n"
        
        prompt += """

请提供：
1. 数据质量评估
2. 主要问题分析
3. 改进建议
4. 风险评估

请用中文回答，格式清晰，重点突出。
"""
        
        return prompt
    
    def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}'
            }
            
            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 4000,
                'temperature': 0.3
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=60,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"AI服务调用失败: HTTP {response.status_code}"
                
        except Exception as e:
            return f"AI服务异常: {str(e)}"

def get_analysis_prompt(data: Dict[str, Any]) -> str:
    """获取分析提示词"""
    analyzer = AIAnalyzer(None)
    return analyzer._build_analysis_prompt(data)
