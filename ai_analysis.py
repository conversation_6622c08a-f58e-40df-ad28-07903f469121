#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析模块
用于分析测试数据并提供智能洞察
"""

import json
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pymysql.cursors
from ai_config import (
    ai_config, analysis_cache, handle_ai_error,
    AIError, AIRateLimitError, AITimeoutError,
    ANALYSIS_PROMPTS, get_analysis_prompt
)

class AIAnalyzer:
    """AI分析器类"""
    
    def __init__(self, db_connection_func):
        self.get_db_connection = db_connection_func
        
    def analyze_top_issues(self, time_range: str = '7', station: str = '',
                          slave: str = '', limit: int = 10) -> Dict[str, Any]:
        """
        分析Top Issue数据

        Args:
            time_range: 时间范围
            station: 测试站位
            slave: EOL设备
            limit: 分析的Issue数量限制

        Returns:
            AI分析结果
        """
        try:
            # 生成缓存参数
            cache_params = {
                'time_range': time_range,
                'station': station,
                'slave': slave,
                'limit': limit,
                'provider': ai_config.get('provider'),
                'model': ai_config.get('model')
            }

            # 检查缓存
            cached_result = analysis_cache.get(cache_params)
            if cached_result:
                cached_result['from_cache'] = True
                return cached_result

            # 1. 收集数据
            analysis_data = self._collect_analysis_data(time_range, station, slave, limit)

            # 2. 构建AI提示
            prompt = get_analysis_prompt(analysis_data)

            # 3. 调用AI服务（带重试机制）
            ai_response = self._call_ai_service_with_retry(prompt)

            # 4. 解析结果
            result = self._parse_ai_response(ai_response, analysis_data)

            final_result = {
                'success': True,
                'analysis': result,
                'data_summary': analysis_data['summary'],
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'from_cache': False
            }

            # 缓存结果
            analysis_cache.set(cache_params, final_result)

            return final_result

        except (AIRateLimitError, AITimeoutError) as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': 'rate_limit' if isinstance(e, AIRateLimitError) else 'timeout',
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except AIError as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': 'ai_service',
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': 'general',
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def _collect_analysis_data(self, time_range: str, station: str,
                              slave: str, limit: int) -> Dict[str, Any]:
        """收集分析所需的数据"""

        # 构建时间条件
        time_condition, params = self._build_time_condition(time_range)

        # 构建站位条件
        station_condition = self._build_station_condition(station, params)

        conn = self.get_db_connection()
        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)

        try:
            # 1. 获取Top Issues
            top_issues_query = f"""
            SELECT
                SUBSTRING_INDEX(Failure_item, ',', 1) as failure_item,
                COUNT(*) as count,
                COUNT(*) * 100.0 / (SELECT COUNT(*) FROM lp8155_eol_log
                                   WHERE TestResult = 'Failed'
                                   AND Failure_item IS NOT NULL
                                   AND Failure_item != ''
                                   AND Failure_item != '-'
                                   {time_condition} {station_condition}) as percentage
            FROM lp8155_eol_log
            WHERE TestResult = 'Failed'
            AND Failure_item IS NOT NULL
            AND Failure_item != ''
            AND Failure_item != '-'
            {time_condition}
            {station_condition}
            GROUP BY SUBSTRING_INDEX(Failure_item, ',', 1)
            ORDER BY count DESC
            LIMIT %s
            """

            cursor.execute(top_issues_query, params + [limit])
            top_issues = cursor.fetchall()

            # 2. 获取详细失败数据（包含具体测试项、测试值等）
            detailed_failures = []
            for issue in top_issues[:5]:  # 只获取前5个主要失败项的详细数据
                failure_item = issue['failure_item']

                detail_query = f"""
                SELECT
                    TestTime,
                    Serial_Number,
                    Station,
                    Failure_item
                FROM lp8155_eol_log
                WHERE TestResult = 'Failed'
                AND SUBSTRING_INDEX(Failure_item, ',', 1) = %s
                {time_condition}
                {station_condition}
                ORDER BY TestTime DESC
                LIMIT 20
                """

                cursor.execute(detail_query, [failure_item] + params)
                details = cursor.fetchall()

                # 解析失败项详细信息
                parsed_details = []
                for detail in details:
                    parsed = self._parse_failure_item(detail['Failure_item'])
                    if parsed:
                        parsed_details.append({
                            'test_time': detail['TestTime'],
                            'serial_number': detail['Serial_Number'],
                            'station': detail['Station'],
                            'main_item': parsed['main_item'],
                            'sub_item': parsed['sub_item'],
                            'standard_value': parsed['standard_value'],
                            'measured_value': parsed['measured_value'],
                            'low_limit': parsed['low_limit'],
                            'high_limit': parsed['high_limit'],
                            'result': parsed['result'],
                            'failure_reason': self._analyze_failure_reason(parsed)
                        })

                detailed_failures.append({
                    'failure_item': failure_item,
                    'count': issue['count'],
                    'percentage': issue['percentage'],
                    'details': parsed_details
                })
            
            # 2. 获取总体统计
            stats_query = f"""
            SELECT
                COUNT(*) as total_tests,
                SUM(CASE WHEN TestResult = 'Passed' THEN 1 ELSE 0 END) as passed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) as failed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as fail_rate
            FROM lp8155_eol_log
            WHERE 1=1 {time_condition} {station_condition}
            """
            
            cursor.execute(stats_query, params)
            stats = cursor.fetchone()
            
            # 3. 获取时间趋势（按天统计）
            trend_query = f"""
            SELECT
                DATE(TestTime) as test_date,
                COUNT(*) as total_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) as failed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as daily_fail_rate
            FROM lp8155_eol_log
            WHERE 1=1 {time_condition} {station_condition}
            GROUP BY DATE(TestTime)
            ORDER BY test_date DESC
            LIMIT 30
            """
            
            cursor.execute(trend_query, params)
            trends = cursor.fetchall()
            
            return {
                'top_issues': top_issues,
                'detailed_failures': detailed_failures,
                'statistics': stats,
                'trends': trends,
                'summary': {
                    'time_range': time_range,
                    'station': station,
                    'total_issues': len(top_issues),
                    'detailed_failures_count': len(detailed_failures),
                    'analysis_period': self._get_period_description(time_range)
                }
            }
            
        finally:
            cursor.close()
            conn.close()
    
    def _build_time_condition(self, time_range: str) -> tuple:
        """构建时间查询条件"""
        params = []
        
        if time_range == '0':  # 今天
            condition = "AND DATE(TestTime) = CURDATE()"
        elif time_range == '1':  # 昨天
            condition = "AND DATE(TestTime) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)"
        elif time_range == '7':  # 最近7天
            condition = "AND TestTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        elif time_range == '30':  # 最近30天
            condition = "AND TestTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
        else:
            condition = "AND TestTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)"  # 默认7天
            
        return condition, params
    
    def _build_station_condition(self, station: str, params: List) -> str:
        """构建站位查询条件"""
        if not station:
            return ""
        
        if station == 'SOC':
            return "AND Station IN ('EOL1', 'EOL2', 'EOL3', 'EOL5', 'EOL#1', 'EOL#2', 'EOL#3', 'EOL#5')"
        elif station == 'VIU':
            return "AND Station IN ('EOL4', 'EOL#4')"
        else:
            params.append(station)
            return "AND Station = %s"
    
    def _get_period_description(self, time_range: str) -> str:
        """获取时间范围描述"""
        descriptions = {
            '0': '今天',
            '1': '昨天', 
            '7': '最近7天',
            '30': '最近30天'
        }
        return descriptions.get(time_range, '最近7天')
    

    
    def _call_ai_service_with_retry(self, prompt: str) -> str:
        """带重试机制的AI服务调用"""

        if not ai_config.is_configured():
            # 如果没有配置，返回模拟结果
            return self._get_mock_response()

        max_retries = ai_config.get('max_retries', 3)
        retry_delay = ai_config.get('retry_delay', 1)

        for attempt in range(max_retries):
            try:
                provider = ai_config.get('provider', 'openai')

                if provider == 'openai':
                    return self._call_openai(prompt)
                elif provider == 'deepseek':
                    return self._call_deepseek(prompt)
                elif provider == 'claude':
                    return self._call_claude(prompt)
                elif provider == 'local':
                    return self._call_local_model(prompt)
                else:
                    return self._get_mock_response()

            except AIRateLimitError:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                    continue
                else:
                    raise
            except AITimeoutError:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    raise
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"AI服务调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(retry_delay)
                    continue
                else:
                    handle_ai_error(e)

        return self._get_mock_response()
    
    def _call_openai(self, prompt: str) -> str:
        """调用OpenAI API"""

        api_key = ai_config.get_api_key()
        provider_config = ai_config.get_provider_config()

        headers = {
            'Authorization': f"Bearer {api_key}",
            'Content-Type': 'application/json'
        }

        data = {
            'model': ai_config.get('model', provider_config['default_model']),
            'messages': [
                {
                    'role': 'system',
                    'content': ANALYSIS_PROMPTS['system']
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': ai_config.get('max_tokens', provider_config['max_tokens']),
            'temperature': ai_config.get('temperature', provider_config['temperature'])
        }

        timeout = ai_config.get('timeout', 30)

        try:
            response = requests.post(
                provider_config['api_url'],
                headers=headers,
                json=data,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                handle_ai_error(None, response)

        except requests.exceptions.Timeout:
            raise AITimeoutError("OpenAI API响应超时")
        except requests.exceptions.RequestException as e:
            handle_ai_error(e)

    def _call_deepseek(self, prompt: str) -> str:
        """调用DeepSeek API"""

        api_key = ai_config.get_api_key()
        provider_config = ai_config.get_provider_config()

        headers = {
            'Authorization': f"Bearer {api_key}",
            'Content-Type': 'application/json'
        }

        data = {
            'model': ai_config.get('model', provider_config['default_model']),
            'messages': [
                {
                    'role': 'system',
                    'content': ANALYSIS_PROMPTS['system']
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': ai_config.get('max_tokens', provider_config['max_tokens']),
            'temperature': ai_config.get('temperature', provider_config['temperature']),
            'stream': False
        }

        timeout = ai_config.get('timeout', 60)  # 增加超时时间到60秒
        print(f"DeepSeek API调用开始，超时设置: {timeout}秒")

        try:
            response = requests.post(
                provider_config['api_url'],
                headers=headers,
                json=data,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"DeepSeek API调用成功，返回内容长度: {len(content)}")
                    return content
                else:
                    raise Exception("DeepSeek API返回格式异常")
            else:
                handle_ai_error(None, response)

        except requests.exceptions.Timeout:
            print("DeepSeek API调用超时")
            raise AITimeoutError("DeepSeek API响应超时，请稍后重试或启用客户端AI调用")
        except requests.exceptions.ConnectionError:
            print("DeepSeek API连接失败")
            raise Exception("DeepSeek API连接失败，请检查网络连接或启用客户端AI调用")
        except requests.exceptions.RequestException as e:
            print(f"DeepSeek API请求异常: {e}")
            handle_ai_error(e)

    def _call_claude(self, prompt: str) -> str:
        """调用Claude API"""

        api_key = ai_config.get_api_key()
        provider_config = ai_config.get_provider_config()

        headers = {
            'x-api-key': api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }

        data = {
            'model': ai_config.get('model', provider_config['default_model']),
            'max_tokens': ai_config.get('max_tokens', provider_config['max_tokens']),
            'temperature': ai_config.get('temperature', provider_config['temperature']),
            'system': ANALYSIS_PROMPTS['system'],
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ]
        }

        timeout = ai_config.get('timeout', 30)

        try:
            response = requests.post(
                provider_config['api_url'],
                headers=headers,
                json=data,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['content'][0]['text']
            else:
                handle_ai_error(None, response)

        except requests.exceptions.Timeout:
            raise AITimeoutError("Claude API响应超时")
        except requests.exceptions.RequestException as e:
            handle_ai_error(e)

    def _call_local_model(self, prompt: str) -> str:
        """调用本地模型API（如Ollama）"""

        provider_config = ai_config.get_provider_config()

        data = {
            'model': ai_config.get('model', provider_config['default_model']),
            'prompt': f"{ANALYSIS_PROMPTS['system']}\n\n{prompt}",
            'stream': False,
            'options': {
                'temperature': ai_config.get('temperature', provider_config['temperature']),
                'num_predict': ai_config.get('max_tokens', provider_config['max_tokens'])
            }
        }

        timeout = ai_config.get('timeout', 60)  # 本地模型可能需要更长时间

        try:
            response = requests.post(
                provider_config['api_url'],
                json=data,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result['response']
            else:
                handle_ai_error(None, response)

        except requests.exceptions.Timeout:
            raise AITimeoutError("本地模型响应超时")
        except requests.exceptions.RequestException as e:
            handle_ai_error(e)
    
    def _get_mock_response(self) -> str:
        """获取模拟AI响应（用于演示）"""
        return """{
    "summary": "基于当前测试数据分析，系统整体表现良好，但存在几个需要重点关注的失败项。主要问题集中在电源和通信相关测试项目上。",
    "key_findings": [
        "电源相关测试项占失败总数的35%以上，是最主要的问题源",
        "通信测试失败率较高，可能存在信号干扰或硬件问题",
        "某些测试项的失败率呈上升趋势，需要及时干预"
    ],
    "trend_analysis": "最近7天的数据显示，整体失败率相对稳定，但个别测试项出现波动。建议持续监控趋势变化。",
    "root_cause_analysis": "电源相关失败可能源于供电稳定性问题或电源模块质量问题；通信失败可能与线束连接、EMC干扰或软件配置相关。",
    "priority_recommendations": [
        "优先解决电源相关测试失败，影响面最大",
        "重点关注通信测试的稳定性改进",
        "建立预防性维护机制"
    ],
    "improvement_suggestions": [
        "加强电源系统的质量控制和测试",
        "优化测试环境的EMC性能",
        "建立实时监控和预警机制",
        "定期校准测试设备"
    ]
}"""
    
    def _parse_ai_response(self, ai_response: str, original_data: Dict) -> Dict[str, Any]:
        """解析AI响应"""
        try:
            # 尝试解析JSON响应
            if ai_response.strip().startswith('{'):
                parsed = json.loads(ai_response)
            else:
                # 如果不是JSON格式，创建基本结构
                parsed = {
                    'summary': ai_response[:500] + '...' if len(ai_response) > 500 else ai_response,
                    'key_findings': ['AI分析结果解析中...'],
                    'trend_analysis': '趋势分析处理中...',
                    'root_cause_analysis': '根因分析处理中...',
                    'priority_recommendations': ['建议分析处理中...'],
                    'improvement_suggestions': ['改进建议处理中...']
                }
            
            # 添加数据统计信息
            parsed['data_insights'] = {
                'total_issues_analyzed': len(original_data['top_issues']),
                'analysis_confidence': 'high' if ai_config.is_configured() else 'demo',
                'data_quality': 'good'
            }
            
            return parsed
            
        except json.JSONDecodeError:
            return {
                'summary': '分析结果解析失败，请检查AI服务配置',
                'key_findings': ['数据解析错误'],
                'trend_analysis': '无法解析趋势数据',
                'root_cause_analysis': '无法解析根因分析',
                'priority_recommendations': ['请检查AI服务配置'],
                'improvement_suggestions': ['请联系系统管理员'],
                'data_insights': {
                    'total_issues_analyzed': 0,
                    'analysis_confidence': 'low',
                    'data_quality': 'error'
                }
            }

    def _parse_failure_item(self, failure_item: str) -> Optional[Dict[str, Any]]:
        """解析失败项字符串，提取详细信息"""
        if not failure_item:
            return None

        parts = failure_item.split(',')
        if len(parts) >= 6:
            try:
                return {
                    'main_item': parts[0].strip() if parts[0] else '',
                    'sub_item': parts[1].strip() if parts[1] else '',
                    'standard_value': float(parts[2]) if parts[2] and parts[2] != '-' else None,
                    'measured_value': float(parts[3]) if parts[3] and parts[3] != '-' else None,
                    'low_limit': float(parts[4]) if parts[4] and parts[4] != '-' else None,
                    'high_limit': float(parts[5]) if parts[5] and parts[5] != '-' else None,
                    'result': parts[6].strip() if len(parts) > 6 and parts[6] else 'Fail'
                }
            except (ValueError, IndexError):
                return {
                    'main_item': parts[0].strip() if parts[0] else '',
                    'sub_item': parts[1].strip() if len(parts) > 1 and parts[1] else '',
                    'standard_value': None,
                    'measured_value': None,
                    'low_limit': None,
                    'high_limit': None,
                    'result': 'Fail'
                }
        return None

    def _analyze_failure_reason(self, parsed_data: Dict[str, Any]) -> str:
        """分析失败原因"""
        if not parsed_data:
            return "数据解析失败"

        main_item = parsed_data.get('main_item', '')
        sub_item = parsed_data.get('sub_item', '')
        standard_value = parsed_data.get('standard_value')
        measured_value = parsed_data.get('measured_value')
        low_limit = parsed_data.get('low_limit')
        high_limit = parsed_data.get('high_limit')

        # 如果有数值信息，分析超限情况
        if all(v is not None for v in [measured_value, low_limit, high_limit]):
            if measured_value < low_limit:
                deviation = ((low_limit - measured_value) / low_limit * 100) if low_limit != 0 else 0
                return f"测试值过低：{measured_value}，低于下限{low_limit}，偏差{deviation:.1f}%"
            elif measured_value > high_limit:
                deviation = ((measured_value - high_limit) / high_limit * 100) if high_limit != 0 else 0
                return f"测试值过高：{measured_value}，超过上限{high_limit}，偏差{deviation:.1f}%"

        # 根据测试项类型分析可能原因
        sub_item_str = str(sub_item) if sub_item else ''
        main_item_str = str(main_item) if main_item else ''

        if 'check1000HzResult' in sub_item_str:
            return "1000Hz频率检测失败，可能是音频系统或信号处理问题"
        elif 'Fre' in sub_item_str:
            return "频率测试失败，可能是信号发生器或接收器问题"
        elif 'THD' in sub_item_str:
            return "总谐波失真超标，可能是音频放大器或信号质量问题"
        elif 'AMP' in sub_item_str:
            return "放大器测试失败，可能是功放电路或负载问题"
        elif 'camera' in main_item_str:
            return "摄像头相关测试失败，可能是图像传感器或连接问题"
        elif 'microphone' in main_item_str:
            return "麦克风相关测试失败，可能是音频采集或信号处理问题"
        elif 'analysis' in main_item_str:
            return "分析类测试失败，可能是算法处理或数据质量问题"
        else:
            return f"{main_item_str} - {sub_item_str} 测试失败"
