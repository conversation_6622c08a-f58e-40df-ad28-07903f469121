<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端AI分析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端AI分析测试</h1>
        <p>测试AI分析功能的直接调用和代理调用</p>

        <div class="test-section">
            <h3>🌐 测试1: 直接调用DeepSeek API</h3>
            <button onclick="testDirectCall()">测试直接调用</button>
            <div id="directResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 测试2: 通过服务器代理调用</h3>
            <button onclick="testProxyCall()">测试代理调用</button>
            <div id="proxyResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💰 测试3: 余额查询</h3>
            <button onclick="testBalance()">查询余额</button>
            <div id="balanceResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 测试4: 模拟完整AI分析流程</h3>
            <button onclick="testFullAnalysis()">完整分析测试</button>
            <div id="fullResult" class="result"></div>
        </div>
    </div>

    <script>
        // 直接调用DeepSeek API
        async function testDirectCall() {
            const resultDiv = document.getElementById('directResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在测试直接调用...';

            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer sk-b28e0b5d4412410db203c87809ccb9ad',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'user',
                                content: '请简单介绍汽车EOL测试，不超过50字。'
                            }
                        ],
                        max_tokens: 100,
                        temperature: 0.3
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 直接调用成功!\n响应: ${content}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 直接调用失败: ${error.message}`;
                console.error('直接调用错误:', error);
            }
        }

        // 通过服务器代理调用
        async function testProxyCall() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在测试代理调用...';

            try {
                const response = await fetch('/api/deepseek/proxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'user',
                                content: '请简单介绍汽车EOL测试，不超过50字。'
                            }
                        ],
                        max_tokens: 100,
                        temperature: 0.3
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 代理调用成功!\n响应: ${content}`;
                } else {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error || errorData.details || '未知错误'}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 代理调用失败: ${error.message}`;
                console.error('代理调用错误:', error);
            }
        }

        // 测试余额查询
        async function testBalance() {
            const resultDiv = document.getElementById('balanceResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在查询余额...';

            try {
                const response = await fetch('/api/deepseek/balance');
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.balance && data.balance.balance_infos) {
                        const balance = data.balance.balance_infos[0].total_balance;
                        const currency = data.balance.balance_infos[0].currency;
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `✅ 余额查询成功!\n当前余额: ${balance} ${currency}`;
                    } else {
                        throw new Error('余额数据格式异常');
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error || '未知错误'}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 余额查询失败: ${error.message}`;
                console.error('余额查询错误:', error);
            }
        }

        // 测试完整AI分析流程
        async function testFullAnalysis() {
            const resultDiv = document.getElementById('fullResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 正在测试完整AI分析流程...';

            try {
                // 1. 先查询余额
                resultDiv.textContent += '\n📊 步骤1: 查询余额...';
                const balanceResponse = await fetch('/api/deepseek/balance');
                if (!balanceResponse.ok) {
                    throw new Error('余额查询失败');
                }

                // 2. 尝试直接调用
                resultDiv.textContent += '\n🌐 步骤2: 尝试直接调用...';
                let aiResponse;
                try {
                    const directResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer sk-b28e0b5d4412410db203c87809ccb9ad',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: 'deepseek-chat',
                            messages: [
                                {
                                    role: 'system',
                                    content: '你是一个专业的汽车测试数据分析专家。'
                                },
                                {
                                    role: 'user',
                                    content: '分析以下测试失败数据：电池电压异常、CAN通信失败、传感器读数错误。请提供改进建议。'
                                }
                            ],
                            max_tokens: 500,
                            temperature: 0.3
                        })
                    });

                    if (directResponse.ok) {
                        const data = await directResponse.json();
                        aiResponse = data.choices[0].message.content;
                        resultDiv.textContent += '\n✅ 直接调用成功';
                    } else {
                        throw new Error('直接调用失败');
                    }
                } catch (directError) {
                    // 3. 如果直接调用失败，使用代理
                    resultDiv.textContent += '\n🔄 步骤3: 直接调用失败，使用代理...';
                    const proxyResponse = await fetch('/api/deepseek/proxy', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: 'deepseek-chat',
                            messages: [
                                {
                                    role: 'system',
                                    content: '你是一个专业的汽车测试数据分析专家。'
                                },
                                {
                                    role: 'user',
                                    content: '分析以下测试失败数据：电池电压异常、CAN通信失败、传感器读数错误。请提供改进建议。'
                                }
                            ],
                            max_tokens: 500,
                            temperature: 0.3
                        })
                    });

                    if (proxyResponse.ok) {
                        const data = await proxyResponse.json();
                        aiResponse = data.choices[0].message.content;
                        resultDiv.textContent += '\n✅ 代理调用成功';
                    } else {
                        throw new Error('代理调用也失败');
                    }
                }

                // 4. 显示结果
                resultDiv.className = 'result success';
                resultDiv.textContent += `\n\n🎉 完整流程测试成功!\n\nAI分析结果:\n${aiResponse}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 完整流程测试失败: ${error.message}`;
                console.error('完整流程错误:', error);
            }
        }
    </script>
</body>
</html>
