#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

import requests
import json
import time

def test_balance_display():
    """测试余额显示格式"""
    print("🔍 测试余额查询API...")
    
    try:
        response = requests.get('http://localhost:5000/api/deepseek/balance')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 余额查询成功")
            print(f"原始响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 模拟前端处理逻辑
            balance = data.get('balance', {})
            if balance.get('balance_infos') and len(balance['balance_infos']) > 0:
                balance_info = balance['balance_infos'][0]
                total_balance = balance_info.get('total_balance', '0.00')
                formatted_text = f"余额信息: {total_balance}元"
                print(f"✅ 格式化后显示: {formatted_text}")
                return True
            else:
                print("❌ 余额信息格式不正确")
                return False
        else:
            print(f"❌ 余额查询失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 余额查询异常: {e}")
        return False

def test_ai_analysis_data():
    """测试AI分析数据获取"""
    print("\n📊 测试AI分析数据获取...")
    
    try:
        params = {
            'test_station': 'EOL',
            'time_range': 'today'
        }
        
        response = requests.get('http://localhost:5000/api/lp8155/ai_analysis_data', params=params)
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                analysis_data = data['data']
                print(f"✅ 数据获取成功")
                print(f"数据结构: {list(analysis_data.keys())}")
                
                # 检查统计数据
                if 'statistics' in analysis_data:
                    stats = analysis_data['statistics']
                    print(f"统计数据: 总测试数={stats.get('total_tests')}, 失败数={stats.get('failed_tests')}")
                
                # 检查Top Issues
                if 'top_issues' in analysis_data:
                    top_issues = analysis_data['top_issues']
                    print(f"Top Issues数量: {len(top_issues)}")
                    if len(top_issues) > 0:
                        first_issue = top_issues[0]
                        print(f"第一个Issue: {first_issue.get('failure_item')} - {first_issue.get('count')}次 ({first_issue.get('percentage')}%)")
                
                return True
            else:
                print(f"❌ 数据获取失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 数据获取请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据获取异常: {e}")
        return False

def test_ai_config():
    """测试AI配置"""
    print("\n⚙️ 测试AI配置...")
    
    try:
        response = requests.get('http://localhost:5000/api/ai_config')
        if response.status_code == 200:
            data = response.json()
            config = data['config']
            print(f"✅ AI配置获取成功")
            print(f"提供商: {config['provider']}")
            print(f"模型: {config['model']}")
            print(f"客户端AI: {config['client_side_ai']}")
            print(f"已配置: {config['is_configured']}")
            return True
        else:
            print(f"❌ AI配置获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ AI配置异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 测试修复后的功能")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 执行测试
    tests = [
        ("余额显示格式", test_balance_display),
        ("AI分析数据获取", test_ai_analysis_data),
        ("AI配置", test_ai_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有修复测试通过！")
        print("\n✅ 修复确认:")
        print("   1. 余额信息显示已简化为 'X.XX元' 格式")
        print("   2. 费用说明已简化为 '0.001-0.01元'")
        print("   3. AI分析数据结构处理已优化")
        print("   4. 错误处理和调试信息已增强")
        
        print("\n🚀 下一步:")
        print("   1. 在浏览器中测试AI分析功能")
        print("   2. 检查收费确认对话框显示")
        print("   3. 验证客户端AI调用是否正常")
        
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关功能。")
    
    print("\n" + "=" * 50)

if __name__ == '__main__':
    main()
