#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek集成和客户端AI调用功能
"""

import requests
import json
import time

def test_deepseek_integration():
    """测试DeepSeek集成功能"""
    print("🧪 测试DeepSeek AI集成功能")
    print("=" * 50)
    
    # 1. 测试AI配置API
    print("\n📋 步骤1: 测试AI配置")
    print("-" * 30)
    
    try:
        # 获取当前配置
        response = requests.get('http://localhost:5000/api/ai_config')
        if response.status_code == 200:
            config_data = response.json()
            print("✅ 获取配置成功")
            print(f"   当前提供商: {config_data['config']['provider']}")
            print(f"   当前模型: {config_data['config']['model']}")
            print(f"   客户端AI: {config_data['config'].get('client_side_ai', False)}")
            
            # 检查DeepSeek是否在可用提供商列表中
            providers = config_data['providers']
            if 'deepseek' in providers:
                print("✅ DeepSeek提供商已配置")
                deepseek_config = providers['deepseek']
                print(f"   DeepSeek名称: {deepseek_config['name']}")
                print(f"   DeepSeek模型: {deepseek_config['models']}")
                print(f"   固定API密钥: {'已配置' if 'fixed_api_key' in deepseek_config else '未配置'}")
            else:
                print("❌ DeepSeek提供商未找到")
                return False
        else:
            print(f"❌ 获取配置失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False
    
    # 2. 测试切换到DeepSeek
    print("\n🔄 步骤2: 切换到DeepSeek提供商")
    print("-" * 30)
    
    try:
        new_config = {
            'provider': 'deepseek',
            'model': 'deepseek-chat',
            'api_key': '',
            'max_tokens': 4000,
            'temperature': 0.3,
            'timeout': 30,
            'cache_enabled': True,
            'cache_duration_hours': 1,
            'client_side_ai': False
        }
        
        response = requests.post(
            'http://localhost:5000/api/ai_config',
            json=new_config
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ 切换到DeepSeek成功")
            else:
                print(f"❌ 切换失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 切换请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 切换测试失败: {e}")
        return False
    
    # 3. 测试AI分析数据获取
    print("\n📊 步骤3: 测试AI分析数据获取")
    print("-" * 30)
    
    try:
        response = requests.get(
            'http://localhost:5000/api/lp8155/ai_analysis_data',
            params={'time_range': '7', 'limit': '5'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("✅ 数据获取成功")
                analysis_data = data['data']
                print(f"   Top Issues数量: {len(analysis_data.get('top_issues', []))}")
                print(f"   总测试数: {analysis_data.get('total_tests', 0)}")
                print(f"   失败测试数: {analysis_data.get('failed_tests', 0)}")
                print(f"   时间信息: {analysis_data.get('time_info', 'N/A')}")
            else:
                print(f"❌ 数据获取失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 数据请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False
    
    # 4. 测试DeepSeek AI分析
    print("\n🤖 步骤4: 测试DeepSeek AI分析")
    print("-" * 30)
    
    try:
        start_time = time.time()
        response = requests.get(
            'http://localhost:5000/api/lp8155/ai_analysis',
            params={'time_range': '7', 'limit': '5'}
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ DeepSeek AI分析成功 (耗时: {end_time - start_time:.2f}秒)")
                analysis = data['analysis']
                print(f"   分析置信度: {analysis['data_insights']['analysis_confidence']}")
                print(f"   分析的问题数: {analysis['data_insights']['total_issues_analyzed']}")
                print(f"   关键发现数量: {len(analysis['key_findings'])}")
                print(f"   优先建议数量: {len(analysis.get('priority_recommendations', []))}")
                
                # 显示部分分析结果
                print("   📝 分析摘要预览:")
                summary_preview = analysis['summary'][:100] + "..." if len(analysis['summary']) > 100 else analysis['summary']
                print(f"      {summary_preview}")
                
            else:
                print(f"❌ AI分析失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ AI分析请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI分析测试失败: {e}")
        return False
    
    # 5. 测试客户端AI配置
    print("\n💻 步骤5: 测试客户端AI配置")
    print("-" * 30)
    
    try:
        # 启用客户端AI
        client_config = {
            'provider': 'deepseek',
            'model': 'deepseek-chat',
            'api_key': '',
            'max_tokens': 4000,
            'temperature': 0.3,
            'timeout': 30,
            'cache_enabled': True,
            'cache_duration_hours': 1,
            'client_side_ai': True  # 启用客户端AI
        }
        
        response = requests.post(
            'http://localhost:5000/api/ai_config',
            json=client_config
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ 客户端AI配置成功")
                
                # 验证配置
                verify_response = requests.get('http://localhost:5000/api/ai_config')
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    client_side_enabled = verify_data['config'].get('client_side_ai', False)
                    print(f"   客户端AI状态: {'启用' if client_side_enabled else '禁用'}")
                    
                    if client_side_enabled:
                        print("✅ 客户端AI配置验证成功")
                    else:
                        print("❌ 客户端AI配置验证失败")
                        return False
                else:
                    print("❌ 配置验证请求失败")
                    return False
            else:
                print(f"❌ 客户端AI配置失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 客户端AI配置请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 客户端AI配置测试失败: {e}")
        return False
    
    # 6. 总结
    print(f"\n🎉 测试完成")
    print("=" * 50)
    print("✅ 所有测试通过！")
    print("\n🔧 新功能特点:")
    print("   • DeepSeek AI提供商已集成")
    print("   • 固定API密钥已配置，可直接使用")
    print("   • 支持客户端AI调用模式")
    print("   • AI分析数据API已就绪")
    print("   • 配置管理功能完善")
    
    print("\n📋 使用说明:")
    print("   1. 在AI配置中选择'DeepSeek AI'提供商")
    print("   2. 启用'客户端AI调用'以在无网络服务器环境中使用")
    print("   3. 点击'AI分析'按钮获得智能分析结果")
    print("   4. DeepSeek模型支持更大的上下文窗口(4000 tokens)")
    
    return True

if __name__ == '__main__':
    success = test_deepseek_integration()
    if success:
        print("\n🚀 DeepSeek集成测试成功完成！")
    else:
        print("\n❌ 测试过程中发现问题，请检查配置。")
