#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试余额查询功能
"""

import requests
import json
import time
import subprocess
import os

def test_balance_api():
    """测试余额查询API"""
    print("🚀 启动exe进程...")
    
    exe_path = os.path.join('dist', 'LeapmotorTestAnalyzer-DeepSeek.exe')
    
    if not os.path.exists(exe_path):
        print(f"❌ exe文件不存在: {exe_path}")
        return False
    
    # 启动exe
    process = subprocess.Popen([exe_path], 
                             stdout=subprocess.PIPE, 
                             stderr=subprocess.PIPE,
                             text=True)
    
    try:
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(10)
        
        # 测试余额查询
        print("💰 测试余额查询...")
        
        response = requests.get('http://localhost:5000/api/deepseek/balance', timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 余额查询成功!")
            print(f"📄 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 解析余额信息
            if data.get('success') and data.get('balance'):
                balance_info = data['balance']
                if 'balance_infos' in balance_info and balance_info['balance_infos']:
                    balance_detail = balance_info['balance_infos'][0]
                    total_balance = balance_detail.get('total_balance', '0.00')
                    currency = balance_detail.get('currency', 'CNY')
                    print(f"💵 当前余额: {total_balance} {currency}")
                    return True
                else:
                    print("⚠️ 余额信息格式异常")
                    return False
            else:
                print(f"❌ 余额查询失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 余额查询失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {error_data}")
            except:
                print(f"错误内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务，请检查exe是否正常启动")
        return False
    except requests.exceptions.Timeout:
        print("❌ 余额查询超时")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 停止进程
        print("🛑 停止exe进程...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()

def test_ai_analysis_dialog():
    """测试AI分析对话框的余额显示"""
    print("\n🤖 测试AI分析对话框...")
    
    # 这里可以添加更多的前端测试逻辑
    print("💡 请手动测试:")
    print("1. 启动exe: dist/LeapmotorTestAnalyzer-DeepSeek.exe")
    print("2. 在浏览器中访问: http://localhost:5000")
    print("3. 点击'AI分析'按钮")
    print("4. 检查弹窗是否显示余额信息")
    print("5. 余额应该显示为: 0.37元 (或类似格式)")

if __name__ == "__main__":
    print("🔍 测试DeepSeek余额查询功能")
    print("=" * 50)
    
    success = test_balance_api()
    
    if success:
        print("\n✅ 余额查询功能修复成功!")
        print("🎉 exe打包后的余额查询问题已解决")
        test_ai_analysis_dialog()
    else:
        print("\n❌ 余额查询功能仍有问题")
        print("🔧 需要进一步调试")
    
    print("\n" + "=" * 50)
    print("测试完成")
