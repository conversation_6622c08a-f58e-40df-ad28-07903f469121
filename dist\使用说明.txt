# 零跑测试数据分析系统 - DeepSeek AI简化版

## 🚀 快速启动

### 方法1: 使用启动脚本（推荐）
双击运行: `启动系统.bat`

### 方法2: 直接运行
双击运行: `LeapmotorTestAnalyzer-DeepSeek.exe`

### 方法3: 命令行运行
```cmd
LeapmotorTestAnalyzer-DeepSeek.exe
```

启动后在浏览器中访问: http://localhost:5000

## 🔧 系统配置

- **AI提供商**: DeepSeek AI (固定，无需配置)
- **API密钥**: *********************************** (预配置)
- **客户端AI**: 默认启用
- **端口**: 5000
- **版本**: 2.0.0-DeepSeek

## 💰 AI分析费用说明

- AI分析功能使用DeepSeek API，会产生少量费用
- 每次分析约消耗 $0.001-0.01
- 系统会在分析前显示余额和费用确认对话框
- 支持余额查询功能

## 📋 主要功能

1. **测试数据查询**: 支持多种筛选条件和时间范围
2. **访问日志管理**: 500条/页，支持排序和刷新
3. **AI智能分析**: DeepSeek驱动的数据分析和报告生成
4. **版本信息**: 显示构建时间和版本号
5. **余额查询**: 实时查询DeepSeek API余额
6. **收费提示**: 使用前显示费用确认对话框

## 🌐 网络配置

### 客户端AI调用（推荐）
- 默认启用，AI请求从浏览器发起
- 适用于服务器网络受限的环境
- 绕过服务器防火墙限制

### 服务器端AI调用
- 如果服务器网络正常，可以禁用客户端模式
- 在AI配置中取消勾选"客户端网络AI调用"

## 🔍 故障排除

### 端口占用问题
如果5000端口被占用：
1. 关闭占用端口的其他程序
2. 或修改app.py中的端口配置

### 数据库连接问题
确保MySQL数据库服务正常运行并检查连接配置

### AI分析失败
1. 检查网络连接
2. 启用"客户端网络AI调用"
3. 确认API密钥余额充足

### 客户端AI调用失败
1. 检查浏览器网络连接
2. 确认防火墙设置
3. 尝试刷新页面重新分析

## 📞 技术支持

如有问题，请联系开发团队。

---
构建时间: 2025-07-04 13:21:49
版本: 2.0.0-DeepSeek AI简化版
特性: DeepSeek集成、客户端AI、余额查询、收费提示
