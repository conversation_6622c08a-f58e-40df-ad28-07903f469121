<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端AI分析测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #log { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🧪 前端AI分析功能测试</h1>
    
    <div class="test-section">
        <h3>测试按钮</h3>
        <button id="testAIBtn" onclick="testAIAnalysis()">测试AI分析流程</button>
        <button onclick="testBalance()">测试余额查询</button>
        <button onclick="testDataFetch()">测试数据获取</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 测试余额查询
        async function testBalance() {
            log('🔍 测试余额查询...');
            try {
                const response = await fetch('/api/deepseek/balance');
                const data = await response.json();
                
                if (data.success) {
                    const balance = data.balance;
                    if (balance.balance_infos && balance.balance_infos.length > 0) {
                        const balanceInfo = balance.balance_infos[0];
                        const totalBalance = balanceInfo.total_balance || '0.00';
                        const balanceText = `余额信息: ${totalBalance}元`;
                        log(`✅ 余额查询成功: ${balanceText}`, 'success');
                    } else {
                        log('❌ 余额信息格式异常', 'error');
                    }
                } else {
                    log(`❌ 余额查询失败: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`❌ 余额查询异常: ${error.message}`, 'error');
            }
        }

        // 测试数据获取
        async function testDataFetch() {
            log('📊 测试数据获取...');
            try {
                const params = new URLSearchParams({
                    time_range: 'today',
                    station: 'EOL',
                    limit: 10
                });
                
                const response = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    const analysisData = data.data;
                    log(`✅ 数据获取成功:`, 'success');
                    log(`   - Top Issues: ${analysisData.top_issues.length}个`);
                    log(`   - 总测试数: ${analysisData.statistics.total_tests}`);
                    log(`   - 失败数: ${analysisData.statistics.failed_tests}`);
                    return analysisData;
                } else {
                    log(`❌ 数据获取失败: ${data.error}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ 数据获取异常: ${error.message}`, 'error');
                return null;
            }
        }

        // 测试完整AI分析流程
        async function testAIAnalysis() {
            log('🚀 开始完整AI分析测试...');
            
            try {
                // 1. 测试余额查询
                log('步骤1: 查询余额...');
                const balanceResponse = await fetch('/api/deepseek/balance');
                const balanceData = await balanceResponse.json();
                
                if (!balanceData.success) {
                    throw new Error('余额查询失败');
                }
                log('✅ 余额查询成功');

                // 2. 测试数据获取
                log('步骤2: 获取分析数据...');
                const params = new URLSearchParams({
                    time_range: 'today',
                    station: 'EOL',
                    limit: 10
                });
                
                const dataResponse = await fetch(`/api/lp8155/ai_analysis_data?${params}`);
                const analysisDataResponse = await dataResponse.json();
                
                if (!analysisDataResponse.success) {
                    throw new Error('数据获取失败');
                }
                log('✅ 数据获取成功');

                // 3. 构建提示
                log('步骤3: 构建AI提示...');
                const analysisData = analysisDataResponse.data;
                const prompt = buildAnalysisPrompt(analysisData);
                log(`✅ 提示构建成功，长度: ${prompt.length} 字符`);

                // 4. 调用DeepSeek API
                log('步骤4: 调用DeepSeek API...');
                const config = {
                    model: 'deepseek-chat',
                    max_tokens: 4000,
                    temperature: 0.3
                };
                
                const aiResult = await callDeepSeekAPI(config, prompt);
                log(`✅ AI调用成功，结果长度: ${aiResult.length} 字符`, 'success');
                log(`AI分析结果预览: ${aiResult.substring(0, 200)}...`);

                log('🎉 完整AI分析测试成功！', 'success');

            } catch (error) {
                log(`❌ AI分析测试失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        }

        // 构建分析提示
        function buildAnalysisPrompt(data) {
            let top_issues, total_tests, failed_tests, time_info;
            
            if (data.statistics) {
                total_tests = parseInt(data.statistics.total_tests) || 0;
                failed_tests = parseInt(data.statistics.failed_tests) || 0;
                top_issues = data.top_issues || [];
                time_info = data.summary?.analysis_period || '未知时间范围';
            } else {
                total_tests = data.total_tests || 0;
                failed_tests = data.failed_tests || 0;
                top_issues = data.top_issues || [];
                time_info = data.time_info || '未知时间范围';
            }

            let prompt = `请分析以下汽车EOL测试数据：\\n\\n`;
            prompt += `时间范围: ${time_info}\\n`;
            prompt += `总测试数: ${total_tests}\\n`;
            prompt += `失败测试数: ${failed_tests}\\n`;
            
            if (total_tests > 0) {
                prompt += `失败率: ${((failed_tests / total_tests) * 100).toFixed(2)}%\\n\\n`;
            } else {
                prompt += `失败率: 0.00%\\n\\n`;
            }

            prompt += `Top失败项分布:\\n`;
            if (top_issues && top_issues.length > 0) {
                top_issues.forEach((issue, index) => {
                    const percentage = parseFloat(issue.percentage) || 0;
                    prompt += `${index + 1}. ${issue.failure_item}: ${issue.count}次 (${percentage.toFixed(2)}%)\\n`;
                });
            } else {
                prompt += `暂无失败项数据\\n`;
            }

            prompt += `\\n请提供专业的分析报告，包括：\\n`;
            prompt += `1. 数据概览和关键发现\\n`;
            prompt += `2. 失败趋势分析\\n`;
            prompt += `3. 可能的根本原因\\n`;
            prompt += `4. 优先级建议\\n`;
            prompt += `5. 改进措施建议\\n\\n`;
            prompt += `请用中文回答，格式清晰，重点突出。`;

            return prompt;
        }

        // 调用DeepSeek API
        async function callDeepSeekAPI(config, prompt) {
            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer sk-b28e0b5d4412410db203c87809ccb9ad`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: config.model || 'deepseek-chat',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的汽车测试数据分析专家，擅长分析EOL测试失败数据并提供改进建议。'
                            },
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: config.max_tokens || 4000,
                        temperature: config.temperature || 0.3
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`DeepSeek API调用失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.choices && data.choices.length > 0) {
                    return data.choices[0].message.content;
                } else {
                    throw new Error('DeepSeek API返回数据格式异常');
                }
            } catch (error) {
                throw error;
            }
        }

        // 页面加载完成
        $(document).ready(function() {
            log('🚀 前端测试页面已加载');
            log('💡 点击测试按钮开始测试各项功能');
        });
    </script>
</body>
</html>
