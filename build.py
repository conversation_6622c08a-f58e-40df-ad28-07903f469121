#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动构建脚本
先递增版本号，然后打包exe
"""

import os
import sys
import subprocess
from build_version import increment_build_version, show_current_version

def build_exe():
    """构建exe文件"""
    try:
        print("开始构建exe文件...")
        
        # 构建命令
        cmd = [
            'pyinstaller', 
            '--onefile', 
            '--add-data', 'static;static',
            '--name', 'TestDataAnalysisSystem',
            'app.py'
        ]
        
        # 执行构建
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("exe构建成功!")
            print("输出文件位置: dist/TestDataAnalysisSystem.exe")
        else:
            print("exe构建失败!")
            print("错误信息:", result.stderr)
            return False
            
        return True
    except Exception as e:
        print(f"构建过程出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 测试数据分析系统构建工具 ===")
    print()
    
    # 显示当前版本
    print("构建前版本信息:")
    show_current_version()
    print()
    
    # 询问是否继续
    response = input("是否继续构建? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("构建已取消")
        return
    
    # 递增版本号
    print("正在递增版本号...")
    new_version = increment_build_version()
    if not new_version:
        print("版本递增失败，构建终止")
        return
    
    print()
    print("新版本信息:")
    show_current_version()
    print()
    
    # 构建exe
    if build_exe():
        print()
        print("=== 构建完成 ===")
        print(f"新版本: {new_version['version']}")
        print(f"发布日期: {new_version['release_date']}")
        print("exe文件: dist/TestDataAnalysisSystem.exe")
    else:
        print("构建失败")

if __name__ == '__main__':
    main()
