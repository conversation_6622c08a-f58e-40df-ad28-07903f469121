# 版本管理说明

## 版本信息字段说明

- **当前版本**: 系统版本号，格式为 v主版本.次版本.构建次数 (如 v1.0.5)
- **发布日期**: exe打包时的时间，包含具体时分秒
- **服务启动**: 每次启动服务时的时间，动态更新
- **构建次数**: 累计构建次数，用于生成版本号

## 版本管理流程

### 开发阶段
- 版本号保持不变
- 每次启动服务时只更新"服务启动"时间
- 不会自动递增版本号

### 打包发布
有两种方式进行版本递增和打包：

#### 方式一：自动构建（推荐）
```bash
python build.py
```
这个脚本会：
1. 显示当前版本信息
2. 询问是否继续构建
3. 自动递增版本号
4. 更新发布日期为当前时间
5. 使用pyinstaller打包exe

#### 方式二：手动构建
```bash
# 1. 递增版本号
python build_version.py increment

# 2. 打包exe
pyinstaller --onefile --add-data "static;static" --name TestDataAnalysisSystem app.py
```

### 查看版本信息
```bash
# 查看当前版本信息
python build_version.py
```

## 版本号规则

- 主版本号: 1 (重大功能更新时手动修改)
- 次版本号: 0 (功能更新时手动修改)  
- 构建次数: 自动递增 (每次打包时+1)

例如: v1.0.1, v1.0.2, v1.0.3...

## 注意事项

1. **版本号固定**: 版本号在打包时确定，运行时不再变化
2. **发布日期固定**: 发布日期在打包时确定，显示具体时分秒
3. **服务启动动态**: 每次启动服务时更新，显示当前启动时间
4. **构建次数累计**: 用于自动生成版本号，确保版本唯一性

## 文件说明

- `version.json`: 版本信息存储文件
- `build_version.py`: 版本管理工具
- `build.py`: 自动构建脚本
- `VERSION_README.md`: 本说明文件
